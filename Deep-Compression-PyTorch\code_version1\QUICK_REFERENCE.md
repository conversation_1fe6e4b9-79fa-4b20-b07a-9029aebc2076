# BERT压缩管道快速参考

## 🚀 快速开始

```bash
# 完整压缩流程 (推荐)
python run_full_compression.py --model_name my_experiment --steps all

# 快速测试 (少量数据)
python run_full_compression.py --model_name quick_test --steps all --max_samples 1000 --epochs 1
```

## 📋 常用命令

### 单步压缩
```bash
# 仅剪枝
python run_full_compression.py --model_name prune_only --steps pruning

# 仅量化 
python run_full_compression.py --model_name quant_only --steps quantization --num_bits 8

# 仅哈夫曼编码
python run_full_compression.py --model_name huffman_only --steps huffman
```

### 组合压缩
```bash
# 剪枝 + 量化
python run_full_compression.py --model_name prune_quant --steps pruning+quantization

# 量化 + 哈夫曼编码
python run_full_compression.py --model_name quant_huffman --steps quantization+huffman

# 剪枝 + 哈夫曼编码
python run_full_compression.py --model_name prune_huffman --steps pruning+huffman
```

## ⚙️ 参数调优

### 剪枝强度
```bash
# 轻度剪枝 (保守)
--prune_method std --sensitivity 0.2

# 中度剪枝 (平衡)
--prune_method std --sensitivity 0.25

# 重度剪枝 (激进)
--prune_method std --sensitivity 0.4

# 百分位数剪枝
--prune_method percentile --percentile 5.0
```

### 量化位宽
```bash
# 高质量 (文件稍大)
--num_bits 8

# 平衡 (推荐)
--num_bits 6

# 高压缩 (可能影响性能)
--num_bits 4
```

### 训练参数
```bash
# 完整训练
--epochs 3 --batch_size 16

# 快速测试  
--epochs 1 --batch_size 32 --max_samples 1000

# 高质量训练
--epochs 5 --batch_size 8
```

## 📂 输出文件

```
/download/model/
├── {model_name}_original.pt        # 原始模型
├── {model_name}_final.pt           # 剪枝+微调后
├── {model_name}_quantized_8bit.pt  # 8位量化模型
└── {model_name}_huffman_*.tar.gz   # 最终压缩包

/download/other/
├── {model_name}_pipeline_*.log     # 执行日志
├── {model_name}_pipeline_config_*.json  # 配置
└── {model_name}_compression_summary_*.txt  # 总结
```

## 🎯 推荐方案

### 初学者
```bash
python run_full_compression.py \
    --model_name beginner_test \
    --steps all \
    --max_samples 2000 \
    --epochs 2
```

### 生产环境
```bash
python run_full_compression.py \
    --model_name production_v1 \
    --steps all \
    --prune_method std \
    --sensitivity 0.25 \
    --num_bits 8 \
    --epochs 3
```

### 高压缩需求
```bash
python run_full_compression.py \
    --model_name high_compression \
    --steps all \
    --prune_method std \
    --sensitivity 0.35 \
    --num_bits 4 \
    --epochs 4
```

### 快速原型
```bash
python run_full_compression.py \
    --model_name prototype \
    --steps pruning+quantization \
    --max_samples 1000 \
    --epochs 1
```

## 💡 使用技巧

1. **命名规范**: 使用描述性的`model_name`，如`bert_prune025_quant8`
2. **渐进测试**: 先用`--max_samples 1000`快速测试参数
3. **监控性能**: 查看压缩总结文件了解压缩比和性能影响
4. **保存配置**: 每次实验的配置会自动保存，便于复现
5. **错误排查**: 查看详细日志文件定位问题

## 🔧 故障排除

```bash
# 内存不足时
--batch_size 8 --max_samples 5000

# CUDA错误时  
--no_cuda

# 快速调试
--max_samples 100 --epochs 1
```

## 📊 性能预期

| 方案 | 压缩比 | 性能损失 | 速度 |
|------|--------|----------|------|
| 仅剪枝 | 2-5x | <2% | 快 |
| 仅量化(8位) | 3-4x | <1% | 中 | 
| 剪枝+量化 | 5-15x | 2-5% | 中 |
| 全套流程 | 10-50x | 3-8% | 慢 |