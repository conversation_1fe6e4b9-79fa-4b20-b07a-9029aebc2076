#!/usr/bin/env python3
"""
快速运行BERT模型哈夫曼编码的脚本
对剪枝+量化后的8位模型进行哈夫曼编码，实现最终压缩
"""

import subprocess
import sys
import os

def run_huffman_encode():
    """运行哈夫曼编码"""
    
    # 8位量化模型路径
    model_path = "/download/model/quantized_model_8bit.pt"
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 错误: 找不到8位量化模型文件 {model_path}")
        print("请确保已经运行了权重量化并保存了 quantized_model_8bit.pt")
        print("💡 提示: 运行 python run_quantization.py 生成8位量化模型")
        return False
    
    print("🚀 开始BERT模型哈夫曼编码...")
    print(f"📂 输入模型: {model_path}")
    print("🎯 目标: 对8位量化权重进行哈夫曼编码，进一步压缩")
    print("📊 预期效果: 额外2-4倍压缩比")
    print("-" * 50)
    
    # 构建命令
    script_path = os.path.join(os.path.dirname(__file__), "bert_huffman_encode.py")
    cmd = [
        sys.executable, script_path,
        "--input_path", model_path,
        "--output_dir", "/download/",
        "--evaluate",
        "--test_decode"
    ]
    
    try:
        # 运行哈夫曼编码脚本
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ 哈夫曼编码完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 哈夫曼编码过程出错: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  哈夫曼编码过程被用户中断")
        return False

def show_compression_pipeline():
    """显示完整的压缩管道"""
    print("\n" + "="*60)
    print("🔄 BERT模型Deep Compression压缩管道总结")
    print("="*60)
    
    # 检查各阶段的模型文件
    original_model = "/download/model/original_model.pt"
    pruned_model = "/download/model/pruned_model.pt" 
    final_model = "/download/model/final_model.pt"
    quantized_fp32 = "/download/model/quantized_model.pt"
    quantized_8bit = "/download/model/quantized_model_8bit.pt"
    
    print("\n📊 压缩阶段检查:")
    stages = [
        ("1️⃣  原始模型", original_model, "bert_pruning.py"),
        ("2️⃣  剪枝后模型", pruned_model, "bert_pruning.py"),
        ("3️⃣  微调后模型", final_model, "bert_pruning.py"),
        ("4️⃣  量化模型(FP32)", quantized_fp32, "bert_weight_quantization.py"),
        ("5️⃣  量化模型(8bit)", quantized_8bit, "bert_weight_quantization.py"),
    ]
    
    for stage_name, file_path, script in stages:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / 1024 / 1024
            print(f"   ✅ {stage_name}: {size_mb:.2f} MB")
        else:
            print(f"   ❌ {stage_name}: 文件不存在 ({script})")
    
    # 检查哈夫曼编码结果
    huffman_dir = "/download/model/huffman_encoded"
    if os.path.exists(huffman_dir):
        huffman_size = 0
        for root, dirs, files in os.walk(huffman_dir):
            for file in files:
                huffman_size += os.path.getsize(os.path.join(root, file))
        huffman_size_mb = huffman_size / 1024 / 1024
        print(f"   ✅ 6️⃣  哈夫曼编码: {huffman_size_mb:.2f} MB")
        
        # 查找压缩包
        import glob
        archives = glob.glob("/download/model/bert_huffman_encoded_*.tar.gz")
        if archives:
            latest_archive = max(archives, key=os.path.getctime)
            archive_size_mb = os.path.getsize(latest_archive) / 1024 / 1024
            print(f"   📦 最终压缩包: {archive_size_mb:.2f} MB")
            
            # 计算总压缩比
            if os.path.exists(original_model):
                original_size_mb = os.path.getsize(original_model) / 1024 / 1024
                total_ratio = original_size_mb / archive_size_mb
                print(f"   🎯 总压缩比: {total_ratio:.2f}x")
    else:
        print(f"   ⏳ 6️⃣  哈夫曼编码: 待完成")
    
    print(f"\n💡 Deep Compression三阶段:")
    print(f"   🔸 剪枝 (Pruning): 移除不重要的权重连接")
    print(f"   🔸 量化 (Quantization): 将32位权重压缩到8位")
    print(f"   🔸 哈夫曼编码 (Huffman): 对量化后的权重进一步熵编码")
    
    return huffman_dir

if __name__ == '__main__':
    # 显示压缩管道状态
    huffman_dir = show_compression_pipeline()
    
    # 运行哈夫曼编码
    success = run_huffman_encode()
    
    if success:
        print("\n🎉 哈夫曼编码成功完成!")
        print("\n📊 哈夫曼编码效果:")
        print("   - 对量化索引进行熵编码压缩")
        print("   - 对码表进行熵编码压缩") 
        print("   - 对稀疏掩码进行熵编码压缩")
        print("   - 总体预期2-4倍额外压缩")
        
        print("\n📁 生成的文件:")
        print("   📂 编码文件目录 (/download/model/huffman_encoded/)")
        print("     - 每层的索引、码表、掩码编码文件")
        print("     - 对应的哈夫曼树和元数据文件")
        print("   📦 压缩包 (/download/model/)")
        print("     - bert_huffman_encoded_YYYYMMDD_HHMMSS.tar.gz 🎯")
        print("   📂 日志和结果 (/download/other/)")
        print("     - bert_huffman_encode_YYYYMMDD_HHMMSS.log")
        print("     - bert_huffman_encode_results_YYYYMMDD_HHMMSS.json")
        print("     - bert_huffman_encode_summary_YYYYMMDD_HHMMSS.txt")
        
        print("\n🏆 Deep Compression完整管道:")
        print("   ✅ 1. 剪枝 → 移除冗余连接")
        print("   ✅ 2. 量化 → 32位→8位权重")
        print("   ✅ 3. 哈夫曼编码 → 熵编码压缩")
        
        # 重新显示最终状态
        print("\n" + "="*50)
        show_compression_pipeline()
        print("="*50)
        
        print("\n💡 使用建议:")
        print("   - 压缩包包含完整的编码模型")
        print("   - 使用专门的解码脚本加载模型")
        print("   - 编码过程保留了模型的所有功能")
        print("   - 压缩比取决于数据的熵和重复程度")
        
    else:
        print("\n❌ 哈夫曼编码失败")
        print("\n🔧 故障排除:")
        print("   1. 检查是否存在 quantized_model_8bit.pt")
        print("   2. 确认权重量化步骤已完成")
        print("   3. 检查磁盘空间是否充足")
        print("   4. 查看详细错误日志")
        sys.exit(1)