"""
BERT 模型权重量化脚本
实现权重共享量化，将32位浮点数压缩到8位（256个不同值）

基于 Deep Compression 论文的权重共享技术
使用 K-means 聚类将权重分组，减少存储位宽
"""

import argparse
import os
import torch
import torch.nn as nn
import numpy as np
from sklearn.cluster import KMeans
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from datasets import load_dataset
from tqdm import tqdm
import logging
import json
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_save_directories():
    """创建保存文件所需的目录结构"""
    dirs = ['/download/model', '/download/other']
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
        logger.info(f"✓ Directory ready: {dir_path}")

def quantize_tensor(tensor, num_bits=8):
    """
    对单个张量进行权重量化，返回索引和码表
    
    Args:
        tensor: 要量化的张量
        num_bits: 量化位数（默认8位，即256个不同值）
    
    Returns:
        tuple: (量化索引(uint8), 码表(float32), 原始形状, 零值掩码)
    """
    # 将张量展平
    original_shape = tensor.shape
    flat_tensor = tensor.view(-1).cpu().numpy()
    
    # 过滤掉零值（保持稀疏性）
    non_zero_mask = flat_tensor != 0
    if non_zero_mask.sum() == 0:
        # 全零张量的特殊处理
        return np.zeros(flat_tensor.shape, dtype=np.uint8), np.array([0.0]), original_shape, non_zero_mask
    
    non_zero_weights = flat_tensor[non_zero_mask]
    
    # 使用 K-means 进行聚类
    num_clusters = 2 ** num_bits  # 8位 = 256个聚类中心
    
    # 如果非零权重数量少于聚类数，减少聚类数
    if len(non_zero_weights) < num_clusters:
        num_clusters = min(len(non_zero_weights), 256)
    
    if num_clusters <= 1:
        codebook = np.array([non_zero_weights[0]] if len(non_zero_weights) > 0 else [0.0])
        indices = np.zeros(flat_tensor.shape, dtype=np.uint8)
        indices[non_zero_mask] = 0
        return indices, codebook, original_shape, non_zero_mask
    
    # 初始化聚类中心（均匀分布在权重范围内）
    min_weight = non_zero_weights.min()
    max_weight = non_zero_weights.max()
    init_centers = np.linspace(min_weight, max_weight, num_clusters).reshape(-1, 1)
    
    # 执行 K-means 聚类
    kmeans = KMeans(
        n_clusters=num_clusters, 
        init=init_centers, 
        n_init=1, 
        random_state=42
    )
    
    kmeans.fit(non_zero_weights.reshape(-1, 1))
    
    # 创建码表（聚类中心）
    codebook = kmeans.cluster_centers_.flatten()
    
    # 创建索引矩阵
    indices = np.zeros(flat_tensor.shape, dtype=np.uint8)
    indices[non_zero_mask] = kmeans.labels_.astype(np.uint8)
    
    return indices, codebook, original_shape, non_zero_mask

def reconstruct_tensor(indices, codebook, original_shape, non_zero_mask, device):
    """
    从量化索引和码表重建张量
    """
    flat_tensor = np.zeros(indices.shape, dtype=np.float32)
    flat_tensor[non_zero_mask] = codebook[indices[non_zero_mask]]
    reconstructed = torch.from_numpy(flat_tensor.reshape(original_shape))
    return reconstructed.to(device)

def apply_weight_quantization(model, num_bits=8, target_modules=None):
    """
    对BERT模型应用权重量化，返回量化信息
    
    Args:
        model: BERT模型
        num_bits: 量化位数
        target_modules: 要量化的模块类型列表，默认量化Linear层
    
    Returns:
        tuple: (量化后的模型, 量化信息字典)
    """
    if target_modules is None:
        target_modules = [nn.Linear]
    
    total_params = 0
    quantized_params = 0
    quantization_info = {}
    
    logger.info(f"开始权重量化 (目标位宽: {num_bits} bits, {2**num_bits} 个不同值)")
    
    for name, module in tqdm(model.named_modules(), desc="量化模块"):
        if any(isinstance(module, target_type) for target_type in target_modules):
            if hasattr(module, 'weight') and module.weight is not None:
                # 统计参数数量
                total_params += module.weight.numel()
                
                # 量化权重
                original_weight = module.weight.data.clone()
                indices, codebook, original_shape, non_zero_mask = quantize_tensor(module.weight.data, num_bits)
                
                # 保存量化信息
                quantization_info[name] = {
                    'indices': indices,
                    'codebook': codebook,
                    'original_shape': original_shape,
                    'non_zero_mask': non_zero_mask
                }
                
                # 重建量化后的权重（用于模型推理）
                quantized_weight = reconstruct_tensor(indices, codebook, original_shape, non_zero_mask, module.weight.device)
                module.weight.data = quantized_weight
                
                # 统计量化后的唯一值数量
                unique_values = len(codebook)
                quantized_params += module.weight.numel()
                
                logger.debug(f"量化 {name}: {original_weight.shape} -> {unique_values} 唯一值")
    
    logger.info(f"量化完成: {quantized_params:,} / {total_params:,} 参数被量化")
    return model, quantization_info

def evaluate_model(model, tokenizer, device, sample_size=1000):
    """
    快速评估模型性能
    """
    logger.info("加载 AG News 数据集进行评估...")
    dataset = load_dataset("ag_news", split="test")
    
    # 随机采样进行快速评估
    if len(dataset) > sample_size:
        indices = np.random.choice(len(dataset), sample_size, replace=False)
        dataset = dataset.select(indices)
    
    model.eval()
    correct = 0
    total = 0
    
    with torch.no_grad():
        for example in tqdm(dataset, desc="评估中"):
            text = example['text']
            label = example['label']
            
            # 分词和编码
            inputs = tokenizer(
                text, 
                return_tensors="pt", 
                truncation=True, 
                padding=True, 
                max_length=512
            ).to(device)
            
            # 预测
            outputs = model(**inputs)
            predicted = outputs.logits.argmax(dim=-1).item()
            
            if predicted == label:
                correct += 1
            total += 1
    
    accuracy = correct / total
    logger.info(f"评估结果: {correct}/{total} = {accuracy:.4f}")
    return accuracy

def calculate_model_size(model):
    """计算模型大小（MB）"""
    param_size = 0
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    
    buffer_size = 0
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
    
    size_mb = (param_size + buffer_size) / 1024 / 1024
    return size_mb

def calculate_quantized_model_size(quantization_info):
    """计算量化模型的实际大小（MB）"""
    total_size_bytes = 0
    
    for name, info in quantization_info.items():
        indices = info['indices']
        codebook = info['codebook']
        non_zero_mask = info['non_zero_mask']
        
        # 索引大小：uint8 = 1 byte per element
        indices_size = indices.nbytes
        
        # 码表大小：float32 = 4 bytes per element
        codebook_size = codebook.nbytes
        
        # 掩码大小：bool = 1 byte per element (保存稀疏性)
        mask_size = non_zero_mask.nbytes
        
        total_size_bytes += indices_size + codebook_size + mask_size
        
        logger.debug(f"{name}: indices={indices_size} + codebook={codebook_size} + mask={mask_size} = {indices_size + codebook_size + mask_size} bytes")
    
    return total_size_bytes / 1024 / 1024

def save_quantized_model(model, quantization_info, save_path):
    """保存真正的8位量化模型"""
    quantized_data = {
        'quantization_info': quantization_info,
        'model_config': model.config.to_dict() if hasattr(model, 'config') else None,
        'num_bits': 8,
        'format': 'uint8_indices_with_codebook'
    }
    
    # 保存非量化部分（如嵌入层等）
    non_quantized_state = {}
    for name, param in model.named_parameters():
        module_name = '.'.join(name.split('.')[:-1])  # 去掉最后的 'weight' 或 'bias'
        if module_name not in quantization_info and 'weight' not in name:
            non_quantized_state[name] = param.data
    
    quantized_data['non_quantized_params'] = non_quantized_state
    
    torch.save(quantized_data, save_path)
    logger.info(f"8位量化模型已保存到: {save_path}")
    
    return save_path

def count_unique_weights(model):
    """统计模型中唯一权重的数量"""
    all_weights = []
    for module in model.modules():
        if isinstance(module, nn.Linear) and hasattr(module, 'weight'):
            weights = module.weight.data.cpu().numpy().flatten()
            all_weights.extend(weights[weights != 0])  # 只统计非零权重
    
    return len(np.unique(all_weights))

def setup_logging(log_file_path):
    """设置文件日志记录"""
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建格式器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    
    # 添加到logger
    logger.addHandler(file_handler)
    
    logger.info(f"日志文件已创建: {log_file_path}")
    return file_handler

def save_quantization_results(results, output_path):
    """保存量化结果到JSON文件"""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    logger.info(f"量化结果已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='BERT模型权重量化')
    parser.add_argument('--model_path', type=str, required=True,
                       help='微调后的BERT模型路径 (state_dict)')
    parser.add_argument('--output_path', type=str, 
                       default='/download/model/quantized_model.pt',
                       help='量化后模型保存路径')
    parser.add_argument('--num_bits', type=int, default=8,
                       help='量化位数 (默认8位)')
    parser.add_argument('--no_cuda', action='store_true', default=False,
                       help='禁用CUDA')
    parser.add_argument('--evaluate', action='store_true', default=True,
                       help='评估量化前后的模型性能')
    
    args = parser.parse_args()
    
    # 创建保存目录
    setup_save_directories()
    
    # 设置日志文件 TODO
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file_path = f"/download/other/bert_quantization_{timestamp}.log"
    file_handler = setup_logging(log_file_path)
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() and not args.no_cuda else 'cpu')
    logger.info(f"使用设备: {device}")
    logger.info(f"量化参数: {args.num_bits} bits")
    logger.info(f"模型路径: {args.model_path}")
    
    # 初始化结果字典
    results = {
        "timestamp": datetime.now().isoformat(),
        "model_path": args.model_path,
        "num_bits": args.num_bits,
        "device": str(device),
        "quantization_params": vars(args)
    }
    
    # 加载模型和分词器
    logger.info("加载BERT模型和分词器...")
    model_name = "textattack/bert-base-uncased-ag-news"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForSequenceClassification.from_pretrained(model_name)
    
    # 加载微调后的权重（支持稀疏模型）
    logger.info(f"加载微调后的模型权重: {args.model_path}")
    try:
        # 先尝试使用weights_only=False加载
        model_data = torch.load(args.model_path, map_location=device, weights_only=False)
        
        # 检测是否为稀疏模型格式
        if isinstance(model_data, dict) and ('sparse_weights' in model_data or 'sparse_tensors' in model_data or 'sparse_data' in model_data):
            logger.info("检测到稀疏模型格式，正在重建...")
            # 导入剪枝模块
            from bert_pruning import BERTPruningModule
            pruning_module = BERTPruningModule(model)
            model = pruning_module.load_sparse_model(args.model_path, model)
            logger.info("稀疏模型加载完成")
        else:
            # 标准模型格式
            if isinstance(model_data, dict) and 'model_state_dict' in model_data:
                model.load_state_dict(model_data['model_state_dict'])
            else:
                model.load_state_dict(model_data)
            logger.info("标准模型加载完成")
    except Exception as e:
        logger.error(f"模型加载失败: {e}")
        logger.info("尝试标准PyTorch加载...")
        try:
            # 尝试标准格式，明确设置weights_only=False
            state_dict = torch.load(args.model_path, map_location=device, weights_only=False)
            if isinstance(state_dict, dict):
                model.load_state_dict(state_dict)
            else:
                # 如果加载的是整个模型对象
                model = state_dict
            logger.info("标准格式加载成功")
        except Exception as e2:
            logger.error(f"标准格式加载也失败: {e2}")
            raise e2
    
    model.to(device)
    
    # 量化前的统计
    original_size = calculate_model_size(model)
    original_unique = count_unique_weights(model)
    logger.info(f"原始模型大小: {original_size:.2f} MB")
    logger.info(f"原始模型唯一权重数: {original_unique:,}")
    
    # 保存原始统计到结果
    results["original_stats"] = {
        "model_size_mb": round(original_size, 2),
        "unique_weights": original_unique
    }
    
    # 评估量化前的性能
    original_accuracy = None
    if args.evaluate:
        logger.info("\n=== 评估量化前模型性能 ===")
        original_accuracy = evaluate_model(model, tokenizer, device)
        results["original_accuracy"] = round(original_accuracy, 4)
    
    # 应用权重量化
    logger.info(f"\n=== 开始权重量化 (目标: {args.num_bits} bits) ===")
    model, quantization_info = apply_weight_quantization(model, args.num_bits)
    
    # 量化后的统计
    quantized_size_fp32 = calculate_model_size(model)  # FP32格式大小（用于推理）
    quantized_size_real = calculate_quantized_model_size(quantization_info)  # 真实8位存储大小
    quantized_unique = count_unique_weights(model)
    
    logger.info(f"量化后模型大小 (FP32格式): {quantized_size_fp32:.2f} MB")
    logger.info(f"量化后模型大小 (8位存储): {quantized_size_real:.2f} MB") 
    logger.info(f"量化后唯一权重数: {quantized_unique:,}")
    
    # 计算压缩比
    size_compression_fp32 = original_size / quantized_size_fp32 if quantized_size_fp32 > 0 else 0
    size_compression_real = original_size / quantized_size_real if quantized_size_real > 0 else 0
    weight_compression = original_unique / quantized_unique if quantized_unique > 0 else 0
    bit_compression = 32 / args.num_bits
    
    # 保存量化后统计到结果
    results["quantized_stats"] = {
        "model_size_fp32_mb": round(quantized_size_fp32, 2),
        "model_size_real_mb": round(quantized_size_real, 2),
        "unique_weights": quantized_unique
    }
    
    results["compression_ratios"] = {
        "model_size_fp32": round(size_compression_fp32, 2),
        "model_size_real": round(size_compression_real, 2),
        "unique_weights": round(weight_compression, 2),
        "theoretical_bits": round(bit_compression, 1)
    }
    
    logger.info(f"\n=== 压缩统计 ===")
    logger.info(f"模型大小压缩比 (FP32): {size_compression_fp32:.2f}x")
    logger.info(f"模型大小压缩比 (真实8位): {size_compression_real:.2f}x")
    logger.info(f"唯一权重压缩比: {weight_compression:.2f}x")
    logger.info(f"理论位宽压缩: 32位 -> {args.num_bits}位 ({bit_compression:.1f}x)")
    
    # 评估量化后的性能
    quantized_accuracy = None
    if args.evaluate:
        logger.info("\n=== 评估量化后模型性能 ===")
        quantized_accuracy = evaluate_model(model, tokenizer, device)
        results["quantized_accuracy"] = round(quantized_accuracy, 4)
        
        if original_accuracy is not None:
            accuracy_drop = original_accuracy - quantized_accuracy
            accuracy_drop_percent = 100 * accuracy_drop / original_accuracy
            
            results["accuracy_comparison"] = {
                "original": round(original_accuracy, 4),
                "quantized": round(quantized_accuracy, 4),
                "drop_absolute": round(accuracy_drop, 4),
                "drop_percent": round(accuracy_drop_percent, 2)
            }
            
            logger.info(f"\n=== 性能对比 ===")
            logger.info(f"量化前准确率: {original_accuracy:.4f}")
            logger.info(f"量化后准确率: {quantized_accuracy:.4f}")
            logger.info(f"准确率下降: {accuracy_drop:.4f} ({accuracy_drop_percent:.2f}%)")
    
    # 保存量化后的模型
    logger.info(f"\n=== 保存模型文件 ===")
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    
    # 保存传统FP32格式（用于兼容性）
    torch.save(model.state_dict(), args.output_path)
    logger.info(f"FP32兼容模型保存到: {args.output_path}")
    
    # 保存真正的8位量化模型
    quantized_model_path = args.output_path.replace('.pt', '_8bit.pt')
    save_quantized_model(model, quantization_info, quantized_model_path)
    
    # 保存模型路径到结果
    results["output_files"] = {
        "fp32_model": args.output_path,
        "quantized_8bit_model": quantized_model_path,
        "log_file": log_file_path
    }
    
    # 保存量化结果到JSON文件 TODO
    results_file_path = f"/download/other/bert_quantization_results_{timestamp}.json"
    save_quantization_results(results, results_file_path)
    
    # 创建详细总结文件
    summary_file_path = f"/download/other/bert_quantization_summary_{timestamp}.txt"
    with open(summary_file_path, 'w', encoding='utf-8') as f:
        f.write("BERT模型权重量化结果总结\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"量化时间: {results['timestamp']}\n")
        f.write(f"模型路径: {results['model_path']}\n")
        f.write(f"量化位数: {results['num_bits']} bits\n")
        f.write(f"设备: {results['device']}\n\n")
        
        f.write("压缩统计:\n")
        f.write("-" * 20 + "\n")
        f.write(f"原始模型大小: {results['original_stats']['model_size_mb']} MB\n")
        f.write(f"量化后大小 (FP32): {results['quantized_stats']['model_size_fp32_mb']} MB\n")
        f.write(f"量化后大小 (8位): {results['quantized_stats']['model_size_real_mb']} MB\n")
        f.write(f"大小压缩比 (FP32): {results['compression_ratios']['model_size_fp32']}x\n")
        f.write(f"大小压缩比 (8位): {results['compression_ratios']['model_size_real']}x\n\n")
        
        f.write(f"原始唯一权重数: {results['original_stats']['unique_weights']:,}\n")
        f.write(f"量化后唯一权重数: {results['quantized_stats']['unique_weights']:,}\n")
        f.write(f"权重压缩比: {results['compression_ratios']['unique_weights']}x\n")
        f.write(f"理论位宽压缩: {results['compression_ratios']['theoretical_bits']}x\n\n")
        
        if 'accuracy_comparison' in results:
            f.write("性能对比:\n")
            f.write("-" * 20 + "\n")
            f.write(f"原始准确率: {results['accuracy_comparison']['original']}\n")
            f.write(f"量化后准确率: {results['accuracy_comparison']['quantized']}\n")
            f.write(f"准确率下降: {results['accuracy_comparison']['drop_absolute']} ")
            f.write(f"({results['accuracy_comparison']['drop_percent']}%)\n\n")
        
        f.write("输出文件:\n")
        f.write("-" * 20 + "\n")
        f.write(f"FP32兼容模型: {results['output_files']['fp32_model']}\n")
        f.write(f"8位量化模型: {results['output_files']['quantized_8bit_model']}\n")
        f.write(f"详细日志: {results['output_files']['log_file']}\n")
        f.write(f"结果JSON: {results_file_path}\n")
        f.write(f"总结文件: {summary_file_path}\n")
    
    logger.info(f"总结文件已保存到: {summary_file_path}")
    
    # 关闭文件日志处理器
    logger.removeHandler(file_handler)
    file_handler.close()
    
    logger.info("\n" + "="*50)
    logger.info("权重量化完成！")
    logger.info(f"📁 所有文件已保存到 /download/ 目录")
    logger.info(f"📋 详细结果: {results_file_path}")
    logger.info(f"📝 总结报告: {summary_file_path}")
    logger.info(f"📄 详细日志: {log_file_path}")
    logger.info(f"🗜️  8位量化模型: {quantized_model_path}")
    logger.info(f"💾 真实压缩比: {size_compression_real:.2f}x")
    logger.info("="*50)

if __name__ == '__main__':
    main()