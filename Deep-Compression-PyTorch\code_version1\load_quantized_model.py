#!/usr/bin/env python3
"""
加载8位量化模型的示例脚本
演示如何正确加载和使用真正的8位量化模型
"""

import torch
import numpy as np
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import os

def reconstruct_tensor(indices, codebook, original_shape, non_zero_mask, device):
    """
    从量化索引和码表重建张量
    """
    flat_tensor = np.zeros(indices.shape, dtype=np.float32)
    flat_tensor[non_zero_mask] = codebook[indices[non_zero_mask]]
    reconstructed = torch.from_numpy(flat_tensor.reshape(original_shape))
    return reconstructed.to(device)

def load_quantized_model(quantized_model_path, device='cpu'):
    """
    加载8位量化模型
    
    Args:
        quantized_model_path: 8位量化模型路径 (xxx_8bit.pt)
        device: 目标设备
    
    Returns:
        重建的BERT模型
    """
    print(f"🔄 加载8位量化模型: {quantized_model_path}")
    
    # 加载量化数据
    quantized_data = torch.load(quantized_model_path, map_location='cpu')
    
    print(f"📊 量化信息:")
    print(f"   - 量化位数: {quantized_data['num_bits']} bits")
    print(f"   - 存储格式: {quantized_data['format']}")
    print(f"   - 量化层数: {len(quantized_data['quantization_info'])}")
    
    # 创建基础模型
    model_name = "textattack/bert-base-uncased-ag-news"
    print(f"🔄 加载基础模型架构: {model_name}")
    model = AutoModelForSequenceClassification.from_pretrained(model_name)
    model.to(device)
    
    # 重建量化的权重
    quantization_info = quantized_data['quantization_info']
    print("🔄 重建量化权重...")
    
    for name, module in model.named_modules():
        if name in quantization_info:
            info = quantization_info[name]
            
            # 从量化信息重建权重
            reconstructed_weight = reconstruct_tensor(
                info['indices'],
                info['codebook'],
                info['original_shape'],
                info['non_zero_mask'],
                device
            )
            
            # 设置重建的权重
            module.weight.data = reconstructed_weight
            
            print(f"   ✓ {name}: {info['original_shape']} -> {len(info['codebook'])} 唯一值")
    
    # 加载非量化参数（如果有）
    if 'non_quantized_params' in quantized_data:
        non_quantized = quantized_data['non_quantized_params']
        for name, param in non_quantized.items():
            # 设置非量化参数
            model.state_dict()[name].copy_(param.to(device))
    
    print("✅ 8位量化模型加载完成!")
    return model

def test_quantized_model(model, tokenizer, device):
    """
    测试量化模型的推理功能
    """
    print("\n🧪 测试量化模型推理...")
    
    # 测试文本
    test_texts = [
        "The stock market reached new highs today.",
        "Scientists discover new planet in distant galaxy.",
        "The football team won the championship game.",
        "New technology breakthrough announced by researchers."
    ]
    
    model.eval()
    class_names = ["World", "Sports", "Business", "Sci/Tech"]
    
    with torch.no_grad():
        for i, text in enumerate(test_texts):
            print(f"\n📝 测试 {i+1}: {text[:50]}...")
            
            # 分词和编码
            inputs = tokenizer(
                text, 
                return_tensors="pt", 
                truncation=True, 
                padding=True, 
                max_length=512
            ).to(device)
            
            # 预测
            outputs = model(**inputs)
            predicted_class = outputs.logits.argmax(dim=-1).item()
            confidence = torch.softmax(outputs.logits, dim=-1).max().item()
            
            print(f"   🎯 预测类别: {class_names[predicted_class]}")
            print(f"   📊 置信度: {confidence:.4f}")

def compare_model_sizes(original_path, quantized_path):
    """
    比较原始模型和量化模型的文件大小
    """
    print("\n📏 模型大小对比:")
    
    if os.path.exists(original_path):
        original_size = os.path.getsize(original_path) / 1024 / 1024
        print(f"   📁 原始模型: {original_size:.2f} MB")
    else:
        print(f"   ❌ 原始模型文件不存在: {original_path}")
        return
    
    if os.path.exists(quantized_path):
        quantized_size = os.path.getsize(quantized_path) / 1024 / 1024
        print(f"   🗜️  8位量化模型: {quantized_size:.2f} MB")
        
        compression_ratio = original_size / quantized_size
        size_reduction = (1 - quantized_size / original_size) * 100
        
        print(f"   📊 压缩比: {compression_ratio:.2f}x")
        print(f"   📉 大小减少: {size_reduction:.1f}%")
    else:
        print(f"   ❌ 量化模型文件不存在: {quantized_path}")

def main():
    print("🚀 8位量化模型加载和测试")
    print("="*50)
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 文件路径
    quantized_model_path = "/download/model/quantized_model_8bit.pt"
    original_model_path = "/download/model/final_model.pt"
    
    # 检查文件是否存在
    if not os.path.exists(quantized_model_path):
        print(f"❌ 错误: 找不到8位量化模型文件 {quantized_model_path}")
        print("请先运行权重量化脚本生成8位量化模型")
        return
    
    try:
        # 加载分词器
        print("\n🔄 加载分词器...")
        tokenizer = AutoTokenizer.from_pretrained("textattack/bert-base-uncased-ag-news")
        
        # 加载量化模型
        model = load_quantized_model(quantized_model_path, device)
        
        # 比较文件大小
        compare_model_sizes(original_model_path, quantized_model_path)
        
        # 测试模型推理
        test_quantized_model(model, tokenizer, device)
        
        print("\n✅ 8位量化模型测试完成!")
        print("💡 模型已成功加载并可正常进行推理")
        
    except Exception as e:
        print(f"\n❌ 加载失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()