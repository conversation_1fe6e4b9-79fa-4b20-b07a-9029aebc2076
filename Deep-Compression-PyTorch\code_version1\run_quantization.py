#!/usr/bin/env python3
"""
快速运行BERT模型量化的脚本
"""

import subprocess
import sys
import os

def run_quantization():
    """运行权重量化"""
    
    # 模型路径
    model_path = "/download/model/final_model.pt"
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 错误: 找不到模型文件 {model_path}")
        print("请确保已经运行了 BERT 剪枝训练并保存了 final_model.pt")
        return False
    
    print("🚀 开始BERT模型权重量化...")
    print(f"📂 模型路径: {model_path}")
    print("🎯 目标: 将32位权重压缩到8位 (75%压缩)")
    print("-" * 50)
    
    # 构建命令 TODO
    script_path = os.path.join(os.path.dirname(__file__), "bert_weight_quantization.py")
    cmd = [
        sys.executable, script_path,
        "--model_path", model_path,
        "--num_bits", "8",
        "--output_path", "/download/model/quantized_model.pt",
        "--evaluate"
    ]
    
    try:
        # 运行量化脚本
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ 权重量化完成!")
        print("📁 量化后的模型已保存到: /download/model/quantized_model.pt")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 量化过程出错: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  量化过程被用户中断")
        return False

if __name__ == '__main__':
    success = run_quantization()
    if success:
        print("\n🎉 权重量化成功完成!")
        print("\n📊 量化效果:")
        print("   - 位宽: 32位 -> 8位 (4倍压缩)")
        print("   - 唯一值数量: 大幅减少到最多256个")
        print("   - 模型大小: 显著减小")
        print("\n📁 生成的文件:")
        print("   📂 模型文件 (/download/model/)")
        print("     - quantized_model.pt: FP32兼容格式（用于推理）")
        print("     - quantized_model_8bit.pt: 真正的8位量化模型 🎯")
        print("\n   📂 日志和结果文件 (/download/other/)")
        print("     - bert_quantization_YYYYMMDD_HHMMSS.log: 详细运行日志")
        print("     - bert_quantization_results_YYYYMMDD_HHMMSS.json: 结构化结果数据")
        print("     - bert_quantization_summary_YYYYMMDD_HHMMSS.txt: 可读性总结报告")
        print("\n💡 提示:")
        print("   - FP32格式: 兼容现有PyTorch代码，但文件大小未压缩")
        print("   - 8位格式: 真正的压缩存储，需要专门的加载代码")
        print("   - 压缩比: 查看日志中的 '真实8位' 压缩统计")
        print("   - 总结文件提供完整的压缩效果分析")
    else:
        print("\n❌ 量化失败，请检查错误信息")
        sys.exit(1)