2025-07-30 14:59:21,969 - __main__ - INFO - 日志文件保存到: /download/other/bert_pruning.log
2025-07-30 14:59:22,047 - __main__ - INFO - 使用设备: cuda
2025-07-30 14:59:22,047 - __main__ - INFO - 加载模型: textattack/bert-base-uncased-ag-news
2025-07-30 14:59:25,968 - __main__ - INFO - === 原始模型信息 ===
2025-07-30 14:59:25,969 - __main__ - INFO - 模型总参数数: 109,485,316
2025-07-30 14:59:25,970 - __main__ - INFO - 可训练参数数: 109,485,316
2025-07-30 14:59:25,970 - __main__ - INFO - 
模型结构概览:
2025-07-30 14:59:25,970 - __main__ - INFO -   bert.encoder.layer.0.attention.self.query: torch.<PERSON>ze([768, 768])
2025-07-30 14:59:25,971 - __main__ - INFO -   bert.encoder.layer.0.attention.self.key: torch.<PERSON><PERSON>([768, 768])
2025-07-30 14:59:25,971 - __main__ - INFO -   bert.encoder.layer.0.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,971 - __main__ - INFO -   bert.encoder.layer.0.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,971 - __main__ - INFO -   bert.encoder.layer.0.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,972 - __main__ - INFO -   bert.encoder.layer.0.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,972 - __main__ - INFO -   bert.encoder.layer.1.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,972 - __main__ - INFO -   bert.encoder.layer.1.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,972 - __main__ - INFO -   bert.encoder.layer.1.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,973 - __main__ - INFO -   bert.encoder.layer.1.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,973 - __main__ - INFO -   bert.encoder.layer.1.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,973 - __main__ - INFO -   bert.encoder.layer.1.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,973 - __main__ - INFO -   bert.encoder.layer.2.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,973 - __main__ - INFO -   bert.encoder.layer.2.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,974 - __main__ - INFO -   bert.encoder.layer.2.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,974 - __main__ - INFO -   bert.encoder.layer.2.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,974 - __main__ - INFO -   bert.encoder.layer.2.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,974 - __main__ - INFO -   bert.encoder.layer.2.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,975 - __main__ - INFO -   bert.encoder.layer.3.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,975 - __main__ - INFO -   bert.encoder.layer.3.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,975 - __main__ - INFO -   bert.encoder.layer.3.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,975 - __main__ - INFO -   bert.encoder.layer.3.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,975 - __main__ - INFO -   bert.encoder.layer.3.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,976 - __main__ - INFO -   bert.encoder.layer.3.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,976 - __main__ - INFO -   bert.encoder.layer.4.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,976 - __main__ - INFO -   bert.encoder.layer.4.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,976 - __main__ - INFO -   bert.encoder.layer.4.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,976 - __main__ - INFO -   bert.encoder.layer.4.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,977 - __main__ - INFO -   bert.encoder.layer.4.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,977 - __main__ - INFO -   bert.encoder.layer.4.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,977 - __main__ - INFO -   bert.encoder.layer.5.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,977 - __main__ - INFO -   bert.encoder.layer.5.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,977 - __main__ - INFO -   bert.encoder.layer.5.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,978 - __main__ - INFO -   bert.encoder.layer.5.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,978 - __main__ - INFO -   bert.encoder.layer.5.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,978 - __main__ - INFO -   bert.encoder.layer.5.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,978 - __main__ - INFO -   bert.encoder.layer.6.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,979 - __main__ - INFO -   bert.encoder.layer.6.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,979 - __main__ - INFO -   bert.encoder.layer.6.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,979 - __main__ - INFO -   bert.encoder.layer.6.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,979 - __main__ - INFO -   bert.encoder.layer.6.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,979 - __main__ - INFO -   bert.encoder.layer.6.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,980 - __main__ - INFO -   bert.encoder.layer.7.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,980 - __main__ - INFO -   bert.encoder.layer.7.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,980 - __main__ - INFO -   bert.encoder.layer.7.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,980 - __main__ - INFO -   bert.encoder.layer.7.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,980 - __main__ - INFO -   bert.encoder.layer.7.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,980 - __main__ - INFO -   bert.encoder.layer.7.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,981 - __main__ - INFO -   bert.encoder.layer.8.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,981 - __main__ - INFO -   bert.encoder.layer.8.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,981 - __main__ - INFO -   bert.encoder.layer.8.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,981 - __main__ - INFO -   bert.encoder.layer.8.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,981 - __main__ - INFO -   bert.encoder.layer.8.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,982 - __main__ - INFO -   bert.encoder.layer.8.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,982 - __main__ - INFO -   bert.encoder.layer.9.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,982 - __main__ - INFO -   bert.encoder.layer.9.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,982 - __main__ - INFO -   bert.encoder.layer.9.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,983 - __main__ - INFO -   bert.encoder.layer.9.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,983 - __main__ - INFO -   bert.encoder.layer.9.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,983 - __main__ - INFO -   bert.encoder.layer.9.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,983 - __main__ - INFO -   bert.encoder.layer.10.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,984 - __main__ - INFO -   bert.encoder.layer.10.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,985 - __main__ - INFO -   bert.encoder.layer.10.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,985 - __main__ - INFO -   bert.encoder.layer.10.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,985 - __main__ - INFO -   bert.encoder.layer.10.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,985 - __main__ - INFO -   bert.encoder.layer.10.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,986 - __main__ - INFO -   bert.encoder.layer.11.attention.self.query: torch.Size([768, 768])
2025-07-30 14:59:25,986 - __main__ - INFO -   bert.encoder.layer.11.attention.self.key: torch.Size([768, 768])
2025-07-30 14:59:25,986 - __main__ - INFO -   bert.encoder.layer.11.attention.self.value: torch.Size([768, 768])
2025-07-30 14:59:25,986 - __main__ - INFO -   bert.encoder.layer.11.attention.output.dense: torch.Size([768, 768])
2025-07-30 14:59:25,986 - __main__ - INFO -   bert.encoder.layer.11.intermediate.dense: torch.Size([3072, 768])
2025-07-30 14:59:25,986 - __main__ - INFO -   bert.encoder.layer.11.output.dense: torch.Size([768, 3072])
2025-07-30 14:59:25,987 - __main__ - INFO -   bert.pooler.dense: torch.Size([768, 768])
2025-07-30 14:59:25,987 - __main__ - INFO -   classifier: torch.Size([4, 768])
2025-07-30 14:59:25,987 - __main__ - INFO - 创建数据加载器...
2025-07-30 14:59:33,393 - __main__ - INFO - 数据加载器创建成功，训练集批次数: 15000, 验证集批次数: 950
2025-07-30 14:59:33,394 - __main__ - INFO - 测试数据加载器...
2025-07-30 14:59:33,397 - __main__ - INFO - 测试批次形状: input_ids=torch.Size([8, 200]), attention_mask=torch.Size([8, 200]), labels=torch.Size([8])
2025-07-30 14:59:33,397 - __main__ - INFO - 
=== 评估原始模型 ===
2025-07-30 14:59:44,492 - __main__ - INFO - 验证准确率: 0.9514 (7231/7600)
2025-07-30 14:59:44,494 - __main__ - INFO - 验证损失: 0.2126
2025-07-30 14:59:45,449 - __main__ - INFO - 原始模型已保存到: /download/model/original_model.pt
2025-07-30 14:59:45,450 - __main__ - INFO - 
=== 开始剪枝 ===
2025-07-30 14:59:45,458 - __main__ - INFO - 使用标准差剪枝，敏感度: 0.25
2025-07-30 14:59:45,503 - __main__ - INFO - 层 bert.encoder.layer.0.attention.self.query: std=0.043016, threshold=0.010754, 剪枝 121930/589824 (20.67%) 参数
2025-07-30 14:59:45,504 - __main__ - INFO - 层 bert.encoder.layer.0.attention.self.key: std=0.042695, threshold=0.010674, 剪枝 124444/589824 (21.10%) 参数
2025-07-30 14:59:45,509 - __main__ - INFO - 层 bert.encoder.layer.0.attention.self.value: std=0.028825, threshold=0.007206, 剪枝 122361/589824 (20.75%) 参数
2025-07-30 14:59:45,510 - __main__ - INFO - 层 bert.encoder.layer.0.attention.output.dense: std=0.028582, threshold=0.007146, 剪枝 126515/589824 (21.45%) 参数
2025-07-30 14:59:45,515 - __main__ - INFO - 层 bert.encoder.layer.0.intermediate.dense: std=0.037405, threshold=0.009351, 剪枝 477210/2359296 (20.23%) 参数
2025-07-30 14:59:45,516 - __main__ - INFO - 层 bert.encoder.layer.0.output.dense: std=0.035791, threshold=0.008948, 剪枝 492192/2359296 (20.86%) 参数
2025-07-30 14:59:45,519 - __main__ - INFO - 层 bert.encoder.layer.1.attention.self.query: std=0.042435, threshold=0.010609, 剪枝 120336/589824 (20.40%) 参数
2025-07-30 14:59:45,520 - __main__ - INFO - 层 bert.encoder.layer.1.attention.self.key: std=0.042427, threshold=0.010607, 剪枝 121341/589824 (20.57%) 参数
2025-07-30 14:59:45,522 - __main__ - INFO - 层 bert.encoder.layer.1.attention.self.value: std=0.028341, threshold=0.007085, 剪枝 120920/589824 (20.50%) 参数
2025-07-30 14:59:45,523 - __main__ - INFO - 层 bert.encoder.layer.1.attention.output.dense: std=0.027964, threshold=0.006991, 剪枝 126134/589824 (21.39%) 参数
2025-07-30 14:59:45,524 - __main__ - INFO - 层 bert.encoder.layer.1.intermediate.dense: std=0.039188, threshold=0.009797, 剪枝 478506/2359296 (20.28%) 参数
2025-07-30 14:59:45,525 - __main__ - INFO - 层 bert.encoder.layer.1.output.dense: std=0.037786, threshold=0.009447, 剪枝 494427/2359296 (20.96%) 参数
2025-07-30 14:59:45,526 - __main__ - INFO - 层 bert.encoder.layer.2.attention.self.query: std=0.047604, threshold=0.011901, 剪枝 126480/589824 (21.44%) 参数
2025-07-30 14:59:45,526 - __main__ - INFO - 层 bert.encoder.layer.2.attention.self.key: std=0.046519, threshold=0.011630, 剪枝 127000/589824 (21.53%) 参数
2025-07-30 14:59:45,527 - __main__ - INFO - 层 bert.encoder.layer.2.attention.self.value: std=0.027902, threshold=0.006975, 剪枝 122780/589824 (20.82%) 参数
2025-07-30 14:59:45,528 - __main__ - INFO - 层 bert.encoder.layer.2.attention.output.dense: std=0.027088, threshold=0.006772, 剪枝 125171/589824 (21.22%) 参数
2025-07-30 14:59:45,529 - __main__ - INFO - 层 bert.encoder.layer.2.intermediate.dense: std=0.039840, threshold=0.009960, 剪枝 480703/2359296 (20.37%) 参数
2025-07-30 14:59:45,529 - __main__ - INFO - 层 bert.encoder.layer.2.output.dense: std=0.038185, threshold=0.009546, 剪枝 496334/2359296 (21.04%) 参数
2025-07-30 14:59:45,530 - __main__ - INFO - 层 bert.encoder.layer.3.attention.self.query: std=0.042909, threshold=0.010727, 剪枝 119791/589824 (20.31%) 参数
2025-07-30 14:59:45,533 - __main__ - INFO - 层 bert.encoder.layer.3.attention.self.key: std=0.042734, threshold=0.010684, 剪枝 120418/589824 (20.42%) 参数
2025-07-30 14:59:45,533 - __main__ - INFO - 层 bert.encoder.layer.3.attention.self.value: std=0.031133, threshold=0.007783, 剪枝 123110/589824 (20.87%) 参数
2025-07-30 14:59:45,534 - __main__ - INFO - 层 bert.encoder.layer.3.attention.output.dense: std=0.029039, threshold=0.007260, 剪枝 124168/589824 (21.05%) 参数
2025-07-30 14:59:45,536 - __main__ - INFO - 层 bert.encoder.layer.3.intermediate.dense: std=0.040416, threshold=0.010104, 剪枝 480991/2359296 (20.39%) 参数
2025-07-30 14:59:45,538 - __main__ - INFO - 层 bert.encoder.layer.3.output.dense: std=0.039217, threshold=0.009804, 剪枝 501417/2359296 (21.25%) 参数
2025-07-30 14:59:45,538 - __main__ - INFO - 层 bert.encoder.layer.4.attention.self.query: std=0.041982, threshold=0.010496, 剪枝 119233/589824 (20.22%) 参数
2025-07-30 14:59:45,539 - __main__ - INFO - 层 bert.encoder.layer.4.attention.self.key: std=0.041737, threshold=0.010434, 剪枝 119051/589824 (20.18%) 参数
2025-07-30 14:59:45,539 - __main__ - INFO - 层 bert.encoder.layer.4.attention.self.value: std=0.034845, threshold=0.008711, 剪枝 121453/589824 (20.59%) 参数
2025-07-30 14:59:45,541 - __main__ - INFO - 层 bert.encoder.layer.4.attention.output.dense: std=0.032467, threshold=0.008117, 剪枝 123974/589824 (21.02%) 参数
2025-07-30 14:59:45,542 - __main__ - INFO - 层 bert.encoder.layer.4.intermediate.dense: std=0.040722, threshold=0.010180, 剪枝 484305/2359296 (20.53%) 参数
2025-07-30 14:59:45,543 - __main__ - INFO - 层 bert.encoder.layer.4.output.dense: std=0.039757, threshold=0.009939, 剪枝 507355/2359296 (21.50%) 参数
2025-07-30 14:59:45,546 - __main__ - INFO - 层 bert.encoder.layer.5.attention.self.query: std=0.043270, threshold=0.010817, 剪枝 119106/589824 (20.19%) 参数
2025-07-30 14:59:45,547 - __main__ - INFO - 层 bert.encoder.layer.5.attention.self.key: std=0.043242, threshold=0.010811, 剪枝 119471/589824 (20.26%) 参数
2025-07-30 14:59:45,548 - __main__ - INFO - 层 bert.encoder.layer.5.attention.self.value: std=0.034850, threshold=0.008713, 剪枝 123256/589824 (20.90%) 参数
2025-07-30 14:59:45,549 - __main__ - INFO - 层 bert.encoder.layer.5.attention.output.dense: std=0.033087, threshold=0.008272, 剪枝 124227/589824 (21.06%) 参数
2025-07-30 14:59:45,550 - __main__ - INFO - 层 bert.encoder.layer.5.intermediate.dense: std=0.040520, threshold=0.010130, 剪枝 482842/2359296 (20.47%) 参数
2025-07-30 14:59:45,550 - __main__ - INFO - 层 bert.encoder.layer.5.output.dense: std=0.039197, threshold=0.009799, 剪枝 505741/2359296 (21.44%) 参数
2025-07-30 14:59:45,551 - __main__ - INFO - 层 bert.encoder.layer.6.attention.self.query: std=0.042888, threshold=0.010722, 剪枝 118331/589824 (20.06%) 参数
2025-07-30 14:59:45,554 - __main__ - INFO - 层 bert.encoder.layer.6.attention.self.key: std=0.042866, threshold=0.010717, 剪枝 118562/589824 (20.10%) 参数
2025-07-30 14:59:45,555 - __main__ - INFO - 层 bert.encoder.layer.6.attention.self.value: std=0.033997, threshold=0.008499, 剪枝 123012/589824 (20.86%) 参数
2025-07-30 14:59:45,556 - __main__ - INFO - 层 bert.encoder.layer.6.attention.output.dense: std=0.032178, threshold=0.008045, 剪枝 124150/589824 (21.05%) 参数
2025-07-30 14:59:45,557 - __main__ - INFO - 层 bert.encoder.layer.6.intermediate.dense: std=0.040728, threshold=0.010182, 剪枝 481441/2359296 (20.41%) 参数
2025-07-30 14:59:45,558 - __main__ - INFO - 层 bert.encoder.layer.6.output.dense: std=0.038693, threshold=0.009673, 剪枝 502270/2359296 (21.29%) 参数
2025-07-30 14:59:45,558 - __main__ - INFO - 层 bert.encoder.layer.7.attention.self.query: std=0.043270, threshold=0.010817, 剪枝 118277/589824 (20.05%) 参数
2025-07-30 14:59:45,559 - __main__ - INFO - 层 bert.encoder.layer.7.attention.self.key: std=0.043407, threshold=0.010852, 剪枝 118240/589824 (20.05%) 参数
2025-07-30 14:59:45,562 - __main__ - INFO - 层 bert.encoder.layer.7.attention.self.value: std=0.032868, threshold=0.008217, 剪枝 121093/589824 (20.53%) 参数
2025-07-30 14:59:45,562 - __main__ - INFO - 层 bert.encoder.layer.7.attention.output.dense: std=0.031649, threshold=0.007912, 剪枝 121790/589824 (20.65%) 参数
2025-07-30 14:59:45,563 - __main__ - INFO - 层 bert.encoder.layer.7.intermediate.dense: std=0.039117, threshold=0.009779, 剪枝 477377/2359296 (20.23%) 参数
2025-07-30 14:59:45,564 - __main__ - INFO - 层 bert.encoder.layer.7.output.dense: std=0.037405, threshold=0.009351, 剪枝 493577/2359296 (20.92%) 参数
2025-07-30 14:59:45,564 - __main__ - INFO - 层 bert.encoder.layer.8.attention.self.query: std=0.043917, threshold=0.010979, 剪枝 118390/589824 (20.07%) 参数
2025-07-30 14:59:45,565 - __main__ - INFO - 层 bert.encoder.layer.8.attention.self.key: std=0.044029, threshold=0.011007, 剪枝 118501/589824 (20.09%) 参数
2025-07-30 14:59:45,565 - __main__ - INFO - 层 bert.encoder.layer.8.attention.self.value: std=0.035322, threshold=0.008830, 剪枝 119993/589824 (20.34%) 参数
2025-07-30 14:59:45,566 - __main__ - INFO - 层 bert.encoder.layer.8.attention.output.dense: std=0.033482, threshold=0.008371, 剪枝 120435/589824 (20.42%) 参数
2025-07-30 14:59:45,568 - __main__ - INFO - 层 bert.encoder.layer.8.intermediate.dense: std=0.039040, threshold=0.009760, 剪枝 471784/2359296 (20.00%) 参数
2025-07-30 14:59:45,572 - __main__ - INFO - 层 bert.encoder.layer.8.output.dense: std=0.037247, threshold=0.009312, 剪枝 486019/2359296 (20.60%) 参数
2025-07-30 14:59:45,574 - __main__ - INFO - 层 bert.encoder.layer.9.attention.self.query: std=0.045765, threshold=0.011441, 剪枝 117034/589824 (19.84%) 参数
2025-07-30 14:59:45,574 - __main__ - INFO - 层 bert.encoder.layer.9.attention.self.key: std=0.045798, threshold=0.011449, 剪枝 118481/589824 (20.09%) 参数
2025-07-30 14:59:45,575 - __main__ - INFO - 层 bert.encoder.layer.9.attention.self.value: std=0.034419, threshold=0.008605, 剪枝 119079/589824 (20.19%) 参数
2025-07-30 14:59:45,577 - __main__ - INFO - 层 bert.encoder.layer.9.attention.output.dense: std=0.032630, threshold=0.008157, 剪枝 118195/589824 (20.04%) 参数
2025-07-30 14:59:45,578 - __main__ - INFO - 层 bert.encoder.layer.9.intermediate.dense: std=0.039692, threshold=0.009923, 剪枝 476749/2359296 (20.21%) 参数
2025-07-30 14:59:45,578 - __main__ - INFO - 层 bert.encoder.layer.9.output.dense: std=0.038859, threshold=0.009715, 剪枝 487693/2359296 (20.67%) 参数
2025-07-30 14:59:45,579 - __main__ - INFO - 层 bert.encoder.layer.10.attention.self.query: std=0.045846, threshold=0.011462, 剪枝 117683/589824 (19.95%) 参数
2025-07-30 14:59:45,582 - __main__ - INFO - 层 bert.encoder.layer.10.attention.self.key: std=0.045598, threshold=0.011399, 剪枝 118018/589824 (20.01%) 参数
2025-07-30 14:59:45,582 - __main__ - INFO - 层 bert.encoder.layer.10.attention.self.value: std=0.035491, threshold=0.008873, 剪枝 124554/589824 (21.12%) 参数
2025-07-30 14:59:45,584 - __main__ - INFO - 层 bert.encoder.layer.10.attention.output.dense: std=0.033266, threshold=0.008317, 剪枝 120978/589824 (20.51%) 参数
2025-07-30 14:59:45,585 - __main__ - INFO - 层 bert.encoder.layer.10.intermediate.dense: std=0.038921, threshold=0.009730, 剪枝 469877/2359296 (19.92%) 参数
2025-07-30 14:59:45,586 - __main__ - INFO - 层 bert.encoder.layer.10.output.dense: std=0.037810, threshold=0.009452, 剪枝 477237/2359296 (20.23%) 参数
2025-07-30 14:59:45,587 - __main__ - INFO - 层 bert.encoder.layer.11.attention.self.query: std=0.045356, threshold=0.011339, 剪枝 118989/589824 (20.17%) 参数
2025-07-30 14:59:45,590 - __main__ - INFO - 层 bert.encoder.layer.11.attention.self.key: std=0.044351, threshold=0.011088, 剪枝 117801/589824 (19.97%) 参数
2025-07-30 14:59:45,591 - __main__ - INFO - 层 bert.encoder.layer.11.attention.self.value: std=0.039073, threshold=0.009768, 剪枝 121051/589824 (20.52%) 参数
2025-07-30 14:59:45,593 - __main__ - INFO - 层 bert.encoder.layer.11.attention.output.dense: std=0.036648, threshold=0.009162, 剪枝 117373/589824 (19.90%) 参数
2025-07-30 14:59:45,593 - __main__ - INFO - 层 bert.encoder.layer.11.intermediate.dense: std=0.039103, threshold=0.009776, 剪枝 467562/2359296 (19.82%) 参数
2025-07-30 14:59:45,594 - __main__ - INFO - 层 bert.encoder.layer.11.output.dense: std=0.035710, threshold=0.008927, 剪枝 472024/2359296 (20.01%) 参数
2025-07-30 14:59:45,596 - __main__ - INFO - 层 bert.pooler.dense: std=0.028567, threshold=0.007142, 剪枝 169237/589824 (28.69%) 参数
2025-07-30 14:59:45,599 - __main__ - INFO - 层 classifier: std=0.020092, threshold=0.005023, 剪枝 602/3072 (19.60%) 参数
2025-07-30 14:59:45,599 - __main__ - INFO - 总体剪枝: 17628152/85527552 (20.61%) 参数
2025-07-30 14:59:45,599 - __main__ - INFO - 压缩比: 1.26x
2025-07-30 14:59:45,605 - __main__ - INFO - 
=== 剪枝统计 ===
2025-07-30 14:59:45,605 - __main__ - INFO - 总参数数: 85,527,552
2025-07-30 14:59:45,606 - __main__ - INFO - 剪枝参数数: 17,628,152
2025-07-30 14:59:45,606 - __main__ - INFO - 剪枝比例: 20.61%
2025-07-30 14:59:45,606 - __main__ - INFO - 压缩比: 1.26x
2025-07-30 14:59:45,606 - __main__ - INFO - 
=== 评估剪枝后模型 ===
2025-07-30 14:59:56,975 - __main__ - INFO - 验证准确率: 0.9517 (7233/7600)
2025-07-30 14:59:56,975 - __main__ - INFO - 验证损失: 0.2037
2025-07-30 14:59:57,600 - __main__ - INFO - 剪枝后模型已保存到: /download/model/pruned_model.pt
2025-07-30 14:59:57,601 - __main__ - INFO - 
=== 开始微调 (3 epochs) ===
2025-07-30 15:09:33,084 - __main__ - INFO - Epoch 1/3, 平均训练损失: 0.0485
2025-07-30 15:09:44,225 - __main__ - INFO - 验证准确率: 0.9446 (7179/7600)
2025-07-30 15:09:44,226 - __main__ - INFO - 验证损失: 0.2178
2025-07-30 15:19:19,767 - __main__ - INFO - Epoch 2/3, 平均训练损失: 0.0390
2025-07-30 15:19:31,068 - __main__ - INFO - 验证准确率: 0.9455 (7186/7600)
2025-07-30 15:19:31,069 - __main__ - INFO - 验证损失: 0.2291
2025-07-30 15:28:59,811 - __main__ - INFO - Epoch 3/3, 平均训练损失: 0.0328
2025-07-30 15:29:10,810 - __main__ - INFO - 验证准确率: 0.9437 (7172/7600)
2025-07-30 15:29:10,810 - __main__ - INFO - 验证损失: 0.2658
2025-07-30 15:29:10,812 - __main__ - INFO - 
=== 评估微调后模型 ===
2025-07-30 15:29:21,852 - __main__ - INFO - 验证准确率: 0.9437 (7172/7600)
2025-07-30 15:29:21,852 - __main__ - INFO - 验证损失: 0.2658
2025-07-30 15:29:22,454 - __main__ - INFO - 最终模型已保存到: /download/model/final_model.pt
2025-07-30 15:29:22,455 - __main__ - INFO - 结果摘要已保存到: /download/other/results_summary.txt
2025-07-30 15:29:22,455 - __main__ - INFO - 
==================================================
2025-07-30 15:29:22,455 - __main__ - INFO - 最终结果总结:
2025-07-30 15:29:22,456 - __main__ - INFO - 原始模型准确率: 0.9514
2025-07-30 15:29:22,456 - __main__ - INFO - 剪枝后准确率: 0.9517
2025-07-30 15:29:22,456 - __main__ - INFO - 微调后准确率: 0.9437
2025-07-30 15:29:22,456 - __main__ - INFO - 准确率下降: 0.0078 (0.82%)
2025-07-30 15:29:22,456 - __main__ - INFO - 模型压缩比: 1.26x
2025-07-30 15:29:22,456 - __main__ - INFO - 剪枝比例: 20.61%
2025-07-30 15:29:22,457 - __main__ - INFO - ==================================================
2025-07-30 15:29:22,457 - __main__ - INFO - 
📁 保存的文件:
2025-07-30 15:29:22,457 - __main__ - INFO -    - 原始模型: /download/model/original_model.pt
2025-07-30 15:29:22,457 - __main__ - INFO -    - 剪枝后模型: /download/model/pruned_model.pt
2025-07-30 15:29:22,457 - __main__ - INFO -    - 最终模型: /download/model/final_model.pt
2025-07-30 15:29:22,457 - __main__ - INFO -    - 详细日志: /download/other/bert_pruning.log
2025-07-30 15:29:22,457 - __main__ - INFO -    - 结果摘要: /download/other/results_summary.txt
2025-07-30 15:29:22,458 - __main__ - INFO - ==================================================
