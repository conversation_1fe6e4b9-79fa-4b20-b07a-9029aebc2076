#!/usr/bin/env python3
"""
BERT剪枝运行脚本
提供不同的运行示例和配置

使用示例：
1. 快速测试（使用少量数据）：
   python run_bert_pruning.py --quick_test

2. 标准差剪枝：
   python run_bert_pruning.py --std_pruning --sensitivity 0.25

3. 百分位数剪枝：
   python run_bert_pruning.py --percentile_pruning --percentile 10.0

4. 完整训练：
   python run_bert_pruning.py --full_training
"""

import subprocess
import argparse
import sys

def run_command(cmd):
    """运行命令并打印输出"""
    print(f"运行命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False, text=True)
        print("✅ 命令执行成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='BERT剪枝运行脚本')
    
    # 运行模式选择
    parser.add_argument('--quick_test', action='store_true', 
                       help='快速测试模式（使用1000个样本，1个epoch）')
    parser.add_argument('--std_pruning', action='store_true',
                       help='使用标准差剪枝')
    parser.add_argument('--percentile_pruning', action='store_true',
                       help='使用百分位数剪枝')
    parser.add_argument('--full_training', action='store_true',
                       help='完整训练模式')
    
    # 参数配置
    parser.add_argument('--sensitivity', type=float, default=0.25,
                       help='标准差剪枝的敏感度 (默认: 0.25)')
    parser.add_argument('--percentile', type=float, default=10.0,
                       help='百分位数剪枝的百分位数 (默认: 10.0)')
    parser.add_argument('--epochs', type=int, default=3,
                       help='训练轮数 (默认: 3)')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批次大小 (默认: 16)')
    
    args = parser.parse_args()
    
    # 基础命令
    base_cmd = [sys.executable, 'bert_pruning.py']
    
    if args.quick_test:
        print("🚀 运行快速测试模式")
        cmd = base_cmd + [
            '--max_samples', '1000',
            '--epochs', '1',
            '--batch_size', '8',
            '--prune_method', 'std',
            '--sensitivity', '0.3'
        ]
    
    elif args.std_pruning:
        print("📊 运行标准差剪枝")
        cmd = base_cmd + [
            '--prune_method', 'std',
            '--sensitivity', str(args.sensitivity),
            '--epochs', str(args.epochs),
            '--batch_size', str(args.batch_size)
        ]
    
    elif args.percentile_pruning:
        print("📈 运行百分位数剪枝")
        cmd = base_cmd + [
            '--prune_method', 'percentile',
            '--percentile', str(args.percentile),
            '--epochs', str(args.epochs),
            '--batch_size', str(args.batch_size)
        ]
    
    elif args.full_training:
        print("💪 运行完整训练模式")
        cmd = base_cmd + [
            '--prune_method', 'std',
            '--sensitivity', '0.25',
            '--epochs', '5',
            '--batch_size', '16'
        ]
    
    else:
        print("❌ 请选择一个运行模式")
        print("使用 --help 查看可用选项")
        return
    
    # 运行命令
    success = run_command(cmd)
    
    if success:
        print("\n🎉 剪枝完成！")
        print("检查 bert_saves/ 目录中的保存模型")
    else:
        print("\n💥 剪枝失败！请检查错误信息")

if __name__ == '__main__':
    main() 