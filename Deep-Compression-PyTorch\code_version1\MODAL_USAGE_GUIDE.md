# Modal平台BERT压缩使用指南

## 概述

本指南介绍如何在Modal平台上使用灵活的BERT模型压缩管道。

## 🚀 快速开始

### 运行命令
```bash
modal run ./code_version1/run_modal.py
```

### 当前配置
- **GPU**: A100-40GB
- **超时**: 18000秒 (5小时)
- **存储**: 持久化卷 `bert_compress`
- **PyTorch**: 2.6.0 (CUDA 12.4)

## 🔧 修改压缩参数

编辑 `run_modal.py` 中的 `run_compression()` 调用：

### 基础用法
```python
# 完整压缩流程
success = run_compression(
    model_name='my_experiment',
    steps='all'
)
```

### 自定义参数
```python
# 高压缩比实验
success = run_compression(
    model_name='high_compression_v1',
    steps='all',
    sensitivity=0.4,    # 更激进的剪枝
    num_bits=4,         # 更低的位宽量化
    epochs=3,           # 微调轮数
    max_samples=10000,  # 使用更多样本
    batch_size=16       # 批次大小
)
```

## 📋 支持的压缩方案

### 1. 单步压缩
```python
# 仅剪枝
run_compression('prune_only', 'pruning', sensitivity=0.25, epochs=3)

# 仅量化
run_compression('quant_only', 'quantization', num_bits=8)

# 仅哈夫曼编码 (需要先有量化模型)
run_compression('huffman_only', 'huffman')
```

### 2. 组合压缩
```python
# 剪枝 + 量化
run_compression('prune_quant', 'pruning+quantization', 
                sensitivity=0.3, num_bits=6)

# 量化 + 哈夫曼编码
run_compression('quant_huffman', 'quantization+huffman', num_bits=4)

# 剪枝 + 哈夫曼编码
run_compression('prune_huffman', 'pruning+huffman', sensitivity=0.25)
```

### 3. 完整流程
```python
# 剪枝 + 量化 + 哈夫曼编码
run_compression('full_compression', 'all', 
                sensitivity=0.25, num_bits=8, epochs=3)
```

## 🔧 参数详解

### 必需参数
- `model_name`: 模型标记名称 (字符串)
- `steps`: 压缩步骤 (字符串)

### 剪枝参数
- `prune_method`: 剪枝方法 (`'std'` 或 `'percentile'`)
- `sensitivity`: 标准差剪枝敏感度 (浮点数, 默认: 0.25)
- `percentile`: 百分位数剪枝百分位数 (浮点数, 默认: 5.0)

### 训练参数
- `epochs`: 微调轮数 (整数, 默认: 3)
- `batch_size`: 批次大小 (整数, 默认: 16)
- `max_samples`: 最大样本数 (整数, 可选)

### 量化参数
- `num_bits`: 量化位数 (整数, 默认: 8)

### 其他参数
- `no_cuda`: 禁用CUDA (布尔值, 默认: False)

## 📂 输出文件位置

所有文件保存在Modal的持久化卷中：

```
/download/model/              # 模型文件
├── {model_name}_original.pt
├── {model_name}_final.pt
├── {model_name}_quantized_8bit.pt
└── {model_name}_huffman_*.tar.gz

/download/other/              # 日志和结果
├── {model_name}_pipeline_*.log
├── {model_name}_pipeline_config_*.json
└── {model_name}_compression_summary_*.txt
```

## 🎯 推荐配置

### 快速测试
```python
run_compression(
    model_name='quick_test',
    steps='all',
    max_samples=2000,
    epochs=1,
    batch_size=32
)
```

### 生产环境
```python
run_compression(
    model_name='production_v1',
    steps='all',
    sensitivity=0.25,
    num_bits=8,
    epochs=3,
    batch_size=16
)
```

### 高压缩实验
```python
run_compression(
    model_name='extreme_compression',
    steps='all',
    sensitivity=0.4,
    num_bits=4,
    epochs=4,
    batch_size=8
)
```

## 💡 使用技巧

1. **命名规范**: 使用描述性的`model_name`，如`modal_prune025_quant8_v1`

2. **渐进测试**: 先用少量样本快速测试参数效果
   ```python
   # 快速参数验证
   run_compression('param_test', 'pruning', max_samples=1000, epochs=1)
   ```

3. **批量实验**: 在一次Modal会话中运行多个实验
   ```python
   # 连续运行多个实验
   experiments = [
       ('exp1', 'pruning', {'sensitivity': 0.2}),
       ('exp2', 'pruning', {'sensitivity': 0.3}),
       ('exp3', 'quantization', {'num_bits': 6}),
       ('exp4', 'quantization', {'num_bits': 4}),
   ]
   
   for name, steps, params in experiments:
       success = run_compression(name, steps, **params)
       print(f"实验 {name}: {'成功' if success else '失败'}")
   ```

4. **错误恢复**: 如果某步失败，可以从中间步骤继续
   ```python
   # 如果完整流程失败，可以单独运行某个步骤
   run_compression('recovery_quant', 'quantization')
   run_compression('recovery_huffman', 'huffman')
   ```

## 🔍 监控和调试

1. **查看日志**: 所有详细日志保存在 `/download/other/` 目录

2. **压缩效果**: 每次实验后会生成压缩总结文件

3. **文件检查**: 确认模型文件是否正确生成

4. **性能评估**: 脚本会自动评估压缩前后的模型性能

## ⚠️ 注意事项

1. **GPU内存**: A100-40GB足够处理BERT模型，但可能需要调整`batch_size`

2. **时间限制**: 当前设置5小时超时，完整流程通常1-3小时

3. **存储空间**: 持久化卷会保留所有实验文件，注意空间管理

4. **版本兼容**: 代码已适配PyTorch 2.6，确保依赖包版本一致

## 📊 性能预期

| 配置 | 预期压缩比 | 预期耗时 | 内存使用 |
|------|------------|----------|----------|
| 快速测试 | 5-10x | 20-40分钟 | 15-25GB |
| 标准配置 | 10-25x | 1-2小时 | 20-30GB |
| 高压缩 | 25-50x | 2-4小时 | 25-35GB |

通过合理配置参数，可以在压缩比和性能之间找到最佳平衡点。