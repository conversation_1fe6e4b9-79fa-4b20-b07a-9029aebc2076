2025-07-31 15:52:12,455 - __main__ - INFO - 日志文件保存到: /download/other/bert_pruning.log
2025-07-31 15:52:12,641 - __main__ - INFO - 使用设备: cuda
2025-07-31 15:52:12,642 - __main__ - INFO - 加载模型: textattack/bert-base-uncased-ag-news
2025-07-31 15:52:20,238 - __main__ - INFO - === 原始模型信息 ===
2025-07-31 15:52:20,240 - __main__ - INFO - 模型总参数数: 109,485,316
2025-07-31 15:52:20,240 - __main__ - INFO - 可训练参数数: 109,485,316
2025-07-31 15:52:20,241 - __main__ - INFO - 
模型结构概览:
2025-07-31 15:52:20,242 - __main__ - INFO -   bert.encoder.layer.0.attention.self.query: torch.<PERSON><PERSON>([768, 768])
2025-07-31 15:52:20,242 - __main__ - INFO -   bert.encoder.layer.0.attention.self.key: torch.<PERSON>ze([768, 768])
2025-07-31 15:52:20,243 - __main__ - INFO -   bert.encoder.layer.0.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,243 - __main__ - INFO -   bert.encoder.layer.0.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,244 - __main__ - INFO -   bert.encoder.layer.0.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,244 - __main__ - INFO -   bert.encoder.layer.0.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,244 - __main__ - INFO -   bert.encoder.layer.1.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,245 - __main__ - INFO -   bert.encoder.layer.1.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,245 - __main__ - INFO -   bert.encoder.layer.1.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,246 - __main__ - INFO -   bert.encoder.layer.1.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,246 - __main__ - INFO -   bert.encoder.layer.1.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,246 - __main__ - INFO -   bert.encoder.layer.1.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,247 - __main__ - INFO -   bert.encoder.layer.2.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,247 - __main__ - INFO -   bert.encoder.layer.2.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,248 - __main__ - INFO -   bert.encoder.layer.2.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,248 - __main__ - INFO -   bert.encoder.layer.2.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,248 - __main__ - INFO -   bert.encoder.layer.2.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,249 - __main__ - INFO -   bert.encoder.layer.2.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,249 - __main__ - INFO -   bert.encoder.layer.3.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,249 - __main__ - INFO -   bert.encoder.layer.3.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,250 - __main__ - INFO -   bert.encoder.layer.3.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,250 - __main__ - INFO -   bert.encoder.layer.3.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,251 - __main__ - INFO -   bert.encoder.layer.3.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,251 - __main__ - INFO -   bert.encoder.layer.3.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,251 - __main__ - INFO -   bert.encoder.layer.4.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,252 - __main__ - INFO -   bert.encoder.layer.4.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,252 - __main__ - INFO -   bert.encoder.layer.4.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,252 - __main__ - INFO -   bert.encoder.layer.4.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,253 - __main__ - INFO -   bert.encoder.layer.4.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,253 - __main__ - INFO -   bert.encoder.layer.4.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,254 - __main__ - INFO -   bert.encoder.layer.5.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,254 - __main__ - INFO -   bert.encoder.layer.5.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,254 - __main__ - INFO -   bert.encoder.layer.5.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,255 - __main__ - INFO -   bert.encoder.layer.5.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,255 - __main__ - INFO -   bert.encoder.layer.5.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,256 - __main__ - INFO -   bert.encoder.layer.5.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,256 - __main__ - INFO -   bert.encoder.layer.6.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,256 - __main__ - INFO -   bert.encoder.layer.6.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,257 - __main__ - INFO -   bert.encoder.layer.6.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,257 - __main__ - INFO -   bert.encoder.layer.6.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,258 - __main__ - INFO -   bert.encoder.layer.6.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,258 - __main__ - INFO -   bert.encoder.layer.6.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,258 - __main__ - INFO -   bert.encoder.layer.7.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,259 - __main__ - INFO -   bert.encoder.layer.7.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,259 - __main__ - INFO -   bert.encoder.layer.7.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,259 - __main__ - INFO -   bert.encoder.layer.7.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,260 - __main__ - INFO -   bert.encoder.layer.7.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,260 - __main__ - INFO -   bert.encoder.layer.7.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,267 - __main__ - INFO -   bert.encoder.layer.8.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,267 - __main__ - INFO -   bert.encoder.layer.8.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,268 - __main__ - INFO -   bert.encoder.layer.8.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,268 - __main__ - INFO -   bert.encoder.layer.8.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,269 - __main__ - INFO -   bert.encoder.layer.8.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,269 - __main__ - INFO -   bert.encoder.layer.8.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,269 - __main__ - INFO -   bert.encoder.layer.9.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,270 - __main__ - INFO -   bert.encoder.layer.9.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,270 - __main__ - INFO -   bert.encoder.layer.9.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,270 - __main__ - INFO -   bert.encoder.layer.9.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,271 - __main__ - INFO -   bert.encoder.layer.9.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,271 - __main__ - INFO -   bert.encoder.layer.9.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,272 - __main__ - INFO -   bert.encoder.layer.10.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,272 - __main__ - INFO -   bert.encoder.layer.10.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,272 - __main__ - INFO -   bert.encoder.layer.10.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,273 - __main__ - INFO -   bert.encoder.layer.10.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,273 - __main__ - INFO -   bert.encoder.layer.10.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,274 - __main__ - INFO -   bert.encoder.layer.10.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,274 - __main__ - INFO -   bert.encoder.layer.11.attention.self.query: torch.Size([768, 768])
2025-07-31 15:52:20,274 - __main__ - INFO -   bert.encoder.layer.11.attention.self.key: torch.Size([768, 768])
2025-07-31 15:52:20,275 - __main__ - INFO -   bert.encoder.layer.11.attention.self.value: torch.Size([768, 768])
2025-07-31 15:52:20,275 - __main__ - INFO -   bert.encoder.layer.11.attention.output.dense: torch.Size([768, 768])
2025-07-31 15:52:20,276 - __main__ - INFO -   bert.encoder.layer.11.intermediate.dense: torch.Size([3072, 768])
2025-07-31 15:52:20,276 - __main__ - INFO -   bert.encoder.layer.11.output.dense: torch.Size([768, 3072])
2025-07-31 15:52:20,276 - __main__ - INFO -   bert.pooler.dense: torch.Size([768, 768])
2025-07-31 15:52:20,277 - __main__ - INFO -   classifier: torch.Size([4, 768])
2025-07-31 15:52:20,277 - __main__ - INFO - 创建数据加载器...
2025-07-31 15:52:32,175 - __main__ - INFO - 数据加载器创建成功，训练集批次数: 15000, 验证集批次数: 950
2025-07-31 15:52:32,176 - __main__ - INFO - 测试数据加载器...
2025-07-31 15:52:32,180 - __main__ - INFO - 测试批次形状: input_ids=torch.Size([8, 200]), attention_mask=torch.Size([8, 200]), labels=torch.Size([8])
2025-07-31 15:52:32,180 - __main__ - INFO - 
=== 评估原始模型 ===
2025-07-31 15:52:44,327 - __main__ - INFO - 验证准确率: 0.9514 (7231/7600)
2025-07-31 15:52:44,328 - __main__ - INFO - 验证损失: 0.2126
2025-07-31 15:52:45,313 - __main__ - INFO - 原始模型已保存到: /download/model/original_model.pt
2025-07-31 15:52:45,313 - __main__ - INFO - 
=== 开始剪枝 ===
2025-07-31 15:52:45,321 - __main__ - INFO - 使用标准差剪枝，敏感度: 1.5
2025-07-31 15:52:45,366 - __main__ - INFO - 层 bert.encoder.layer.0.attention.self.query: std=0.043016, threshold=0.064523, 剪枝 514916/589824 (87.30%) 参数
2025-07-31 15:52:45,367 - __main__ - INFO - 层 bert.encoder.layer.0.attention.self.key: std=0.042695, threshold=0.064043, 剪枝 516792/589824 (87.62%) 参数
2025-07-31 15:52:45,368 - __main__ - INFO - 层 bert.encoder.layer.0.attention.self.value: std=0.028825, threshold=0.043237, 剪枝 511865/589824 (86.78%) 参数
2025-07-31 15:52:45,369 - __main__ - INFO - 层 bert.encoder.layer.0.attention.output.dense: std=0.028582, threshold=0.042873, 剪枝 519625/589824 (88.10%) 参数
2025-07-31 15:52:45,371 - __main__ - INFO - 层 bert.encoder.layer.0.intermediate.dense: std=0.037405, threshold=0.056107, 剪枝 2048470/2359296 (86.83%) 参数
2025-07-31 15:52:45,372 - __main__ - INFO - 层 bert.encoder.layer.0.output.dense: std=0.035791, threshold=0.053686, 剪枝 2065187/2359296 (87.53%) 参数
2025-07-31 15:52:45,373 - __main__ - INFO - 层 bert.encoder.layer.1.attention.self.query: std=0.042435, threshold=0.063653, 剪枝 513863/589824 (87.12%) 参数
2025-07-31 15:52:45,374 - __main__ - INFO - 层 bert.encoder.layer.1.attention.self.key: std=0.042427, threshold=0.063640, 剪枝 514633/589824 (87.25%) 参数
2025-07-31 15:52:45,375 - __main__ - INFO - 层 bert.encoder.layer.1.attention.self.value: std=0.028341, threshold=0.042511, 剪枝 512792/589824 (86.94%) 参数
2025-07-31 15:52:45,376 - __main__ - INFO - 层 bert.encoder.layer.1.attention.output.dense: std=0.027964, threshold=0.041946, 剪枝 519564/589824 (88.09%) 参数
2025-07-31 15:52:45,377 - __main__ - INFO - 层 bert.encoder.layer.1.intermediate.dense: std=0.039188, threshold=0.058782, 剪枝 2050208/2359296 (86.90%) 参数
2025-07-31 15:52:45,378 - __main__ - INFO - 层 bert.encoder.layer.1.output.dense: std=0.037786, threshold=0.056679, 剪枝 2070445/2359296 (87.76%) 参数
2025-07-31 15:52:45,379 - __main__ - INFO - 层 bert.encoder.layer.2.attention.self.query: std=0.047604, threshold=0.071406, 剪枝 519624/589824 (88.10%) 参数
2025-07-31 15:52:45,379 - __main__ - INFO - 层 bert.encoder.layer.2.attention.self.key: std=0.046519, threshold=0.069778, 剪枝 519555/589824 (88.09%) 参数
2025-07-31 15:52:45,380 - __main__ - INFO - 层 bert.encoder.layer.2.attention.self.value: std=0.027902, threshold=0.041853, 剪枝 512446/589824 (86.88%) 参数
2025-07-31 15:52:45,381 - __main__ - INFO - 层 bert.encoder.layer.2.attention.output.dense: std=0.027088, threshold=0.040632, 剪枝 516057/589824 (87.49%) 参数
2025-07-31 15:52:45,382 - __main__ - INFO - 层 bert.encoder.layer.2.intermediate.dense: std=0.039840, threshold=0.059760, 剪枝 2052148/2359296 (86.98%) 参数
2025-07-31 15:52:45,383 - __main__ - INFO - 层 bert.encoder.layer.2.output.dense: std=0.038185, threshold=0.057278, 剪枝 2066498/2359296 (87.59%) 参数
2025-07-31 15:52:45,384 - __main__ - INFO - 层 bert.encoder.layer.3.attention.self.query: std=0.042909, threshold=0.064363, 剪枝 513663/589824 (87.09%) 参数
2025-07-31 15:52:45,385 - __main__ - INFO - 层 bert.encoder.layer.3.attention.self.key: std=0.042734, threshold=0.064101, 剪枝 514104/589824 (87.16%) 参数
2025-07-31 15:52:45,386 - __main__ - INFO - 层 bert.encoder.layer.3.attention.self.value: std=0.031133, threshold=0.046700, 剪枝 513351/589824 (87.03%) 参数
2025-07-31 15:52:45,386 - __main__ - INFO - 层 bert.encoder.layer.3.attention.output.dense: std=0.029039, threshold=0.043558, 剪枝 515588/589824 (87.41%) 参数
2025-07-31 15:52:45,387 - __main__ - INFO - 层 bert.encoder.layer.3.intermediate.dense: std=0.040416, threshold=0.060623, 剪枝 2052696/2359296 (87.00%) 参数
2025-07-31 15:52:45,388 - __main__ - INFO - 层 bert.encoder.layer.3.output.dense: std=0.039217, threshold=0.058826, 剪枝 2073879/2359296 (87.90%) 参数
2025-07-31 15:52:45,389 - __main__ - INFO - 层 bert.encoder.layer.4.attention.self.query: std=0.041982, threshold=0.062973, 剪枝 512228/589824 (86.84%) 参数
2025-07-31 15:52:45,390 - __main__ - INFO - 层 bert.encoder.layer.4.attention.self.key: std=0.041737, threshold=0.062606, 剪枝 512508/589824 (86.89%) 参数
2025-07-31 15:52:45,391 - __main__ - INFO - 层 bert.encoder.layer.4.attention.self.value: std=0.034845, threshold=0.052268, 剪枝 513210/589824 (87.01%) 参数
2025-07-31 15:52:45,392 - __main__ - INFO - 层 bert.encoder.layer.4.attention.output.dense: std=0.032467, threshold=0.048700, 剪枝 514426/589824 (87.22%) 参数
2025-07-31 15:52:45,392 - __main__ - INFO - 层 bert.encoder.layer.4.intermediate.dense: std=0.040722, threshold=0.061082, 剪枝 2052047/2359296 (86.98%) 参数
2025-07-31 15:52:45,393 - __main__ - INFO - 层 bert.encoder.layer.4.output.dense: std=0.039757, threshold=0.059636, 剪枝 2073358/2359296 (87.88%) 参数
2025-07-31 15:52:45,394 - __main__ - INFO - 层 bert.encoder.layer.5.attention.self.query: std=0.043270, threshold=0.064905, 剪枝 512531/589824 (86.90%) 参数
2025-07-31 15:52:45,395 - __main__ - INFO - 层 bert.encoder.layer.5.attention.self.key: std=0.043242, threshold=0.064863, 剪枝 512665/589824 (86.92%) 参数
2025-07-31 15:52:45,396 - __main__ - INFO - 层 bert.encoder.layer.5.attention.self.value: std=0.034850, threshold=0.052275, 剪枝 512778/589824 (86.94%) 参数
2025-07-31 15:52:45,396 - __main__ - INFO - 层 bert.encoder.layer.5.attention.output.dense: std=0.033087, threshold=0.049630, 剪枝 514239/589824 (87.19%) 参数
2025-07-31 15:52:45,397 - __main__ - INFO - 层 bert.encoder.layer.5.intermediate.dense: std=0.040520, threshold=0.060780, 剪枝 2052288/2359296 (86.99%) 参数
2025-07-31 15:52:45,398 - __main__ - INFO - 层 bert.encoder.layer.5.output.dense: std=0.039197, threshold=0.058795, 剪枝 2072145/2359296 (87.83%) 参数
2025-07-31 15:52:45,399 - __main__ - INFO - 层 bert.encoder.layer.6.attention.self.query: std=0.042888, threshold=0.064333, 剪枝 511893/589824 (86.79%) 参数
2025-07-31 15:52:45,400 - __main__ - INFO - 层 bert.encoder.layer.6.attention.self.key: std=0.042866, threshold=0.064300, 剪枝 512165/589824 (86.83%) 参数
2025-07-31 15:52:45,401 - __main__ - INFO - 层 bert.encoder.layer.6.attention.self.value: std=0.033997, threshold=0.050996, 剪枝 512749/589824 (86.93%) 参数
2025-07-31 15:52:45,402 - __main__ - INFO - 层 bert.encoder.layer.6.attention.output.dense: std=0.032178, threshold=0.048268, 剪枝 513941/589824 (87.13%) 参数
2025-07-31 15:52:45,402 - __main__ - INFO - 层 bert.encoder.layer.6.intermediate.dense: std=0.040728, threshold=0.061092, 剪枝 2051123/2359296 (86.94%) 参数
2025-07-31 15:52:45,403 - __main__ - INFO - 层 bert.encoder.layer.6.output.dense: std=0.038693, threshold=0.058039, 剪枝 2067227/2359296 (87.62%) 参数
2025-07-31 15:52:45,404 - __main__ - INFO - 层 bert.encoder.layer.7.attention.self.query: std=0.043270, threshold=0.064905, 剪枝 512166/589824 (86.83%) 参数
2025-07-31 15:52:45,405 - __main__ - INFO - 层 bert.encoder.layer.7.attention.self.key: std=0.043407, threshold=0.065110, 剪枝 512383/589824 (86.87%) 参数
2025-07-31 15:52:45,406 - __main__ - INFO - 层 bert.encoder.layer.7.attention.self.value: std=0.032868, threshold=0.049302, 剪枝 511799/589824 (86.77%) 参数
2025-07-31 15:52:45,407 - __main__ - INFO - 层 bert.encoder.layer.7.attention.output.dense: std=0.031649, threshold=0.047474, 剪枝 513260/589824 (87.02%) 参数
2025-07-31 15:52:45,408 - __main__ - INFO - 层 bert.encoder.layer.7.intermediate.dense: std=0.039117, threshold=0.058675, 剪枝 2048855/2359296 (86.84%) 参数
2025-07-31 15:52:45,409 - __main__ - INFO - 层 bert.encoder.layer.7.output.dense: std=0.037405, threshold=0.056107, 剪枝 2062008/2359296 (87.40%) 参数
2025-07-31 15:52:45,409 - __main__ - INFO - 层 bert.encoder.layer.8.attention.self.query: std=0.043917, threshold=0.065875, 剪枝 511927/589824 (86.79%) 参数
2025-07-31 15:52:45,410 - __main__ - INFO - 层 bert.encoder.layer.8.attention.self.key: std=0.044029, threshold=0.066044, 剪枝 512399/589824 (86.87%) 参数
2025-07-31 15:52:45,411 - __main__ - INFO - 层 bert.encoder.layer.8.attention.self.value: std=0.035322, threshold=0.052983, 剪枝 512080/589824 (86.82%) 参数
2025-07-31 15:52:45,412 - __main__ - INFO - 层 bert.encoder.layer.8.attention.output.dense: std=0.033482, threshold=0.050223, 剪枝 513224/589824 (87.01%) 参数
2025-07-31 15:52:45,413 - __main__ - INFO - 层 bert.encoder.layer.8.intermediate.dense: std=0.039040, threshold=0.058559, 剪枝 2047747/2359296 (86.79%) 参数
2025-07-31 15:52:45,414 - __main__ - INFO - 层 bert.encoder.layer.8.output.dense: std=0.037247, threshold=0.055871, 剪枝 2060799/2359296 (87.35%) 参数
2025-07-31 15:52:45,415 - __main__ - INFO - 层 bert.encoder.layer.9.attention.self.query: std=0.045765, threshold=0.068647, 剪枝 511792/589824 (86.77%) 参数
2025-07-31 15:52:45,416 - __main__ - INFO - 层 bert.encoder.layer.9.attention.self.key: std=0.045798, threshold=0.068696, 剪枝 512303/589824 (86.86%) 参数
2025-07-31 15:52:45,416 - __main__ - INFO - 层 bert.encoder.layer.9.attention.self.value: std=0.034419, threshold=0.051628, 剪枝 511649/589824 (86.75%) 参数
2025-07-31 15:52:45,417 - __main__ - INFO - 层 bert.encoder.layer.9.attention.output.dense: std=0.032630, threshold=0.048945, 剪枝 512229/589824 (86.84%) 参数
2025-07-31 15:52:45,418 - __main__ - INFO - 层 bert.encoder.layer.9.intermediate.dense: std=0.039692, threshold=0.059538, 剪枝 2057351/2359296 (87.20%) 参数
2025-07-31 15:52:45,419 - __main__ - INFO - 层 bert.encoder.layer.9.output.dense: std=0.038859, threshold=0.058288, 剪枝 2070066/2359296 (87.74%) 参数
2025-07-31 15:52:45,420 - __main__ - INFO - 层 bert.encoder.layer.10.attention.self.query: std=0.045846, threshold=0.068770, 剪枝 512655/589824 (86.92%) 参数
2025-07-31 15:52:45,421 - __main__ - INFO - 层 bert.encoder.layer.10.attention.self.key: std=0.045598, threshold=0.068397, 剪枝 511708/589824 (86.76%) 参数
2025-07-31 15:52:45,421 - __main__ - INFO - 层 bert.encoder.layer.10.attention.self.value: std=0.035491, threshold=0.053237, 剪枝 513641/589824 (87.08%) 参数
2025-07-31 15:52:45,422 - __main__ - INFO - 层 bert.encoder.layer.10.attention.output.dense: std=0.033266, threshold=0.049899, 剪枝 512219/589824 (86.84%) 参数
2025-07-31 15:52:45,423 - __main__ - INFO - 层 bert.encoder.layer.10.intermediate.dense: std=0.038921, threshold=0.058382, 剪枝 2048634/2359296 (86.83%) 参数
2025-07-31 15:52:45,424 - __main__ - INFO - 层 bert.encoder.layer.10.output.dense: std=0.037810, threshold=0.056715, 剪枝 2060309/2359296 (87.33%) 参数
2025-07-31 15:52:45,425 - __main__ - INFO - 层 bert.encoder.layer.11.attention.self.query: std=0.045356, threshold=0.068034, 剪枝 513473/589824 (87.06%) 参数
2025-07-31 15:52:45,425 - __main__ - INFO - 层 bert.encoder.layer.11.attention.self.key: std=0.044351, threshold=0.066527, 剪枝 512158/589824 (86.83%) 参数
2025-07-31 15:52:45,426 - __main__ - INFO - 层 bert.encoder.layer.11.attention.self.value: std=0.039073, threshold=0.058609, 剪枝 512198/589824 (86.84%) 参数
2025-07-31 15:52:45,427 - __main__ - INFO - 层 bert.encoder.layer.11.attention.output.dense: std=0.036648, threshold=0.054972, 剪枝 511662/589824 (86.75%) 参数
2025-07-31 15:52:45,428 - __main__ - INFO - 层 bert.encoder.layer.11.intermediate.dense: std=0.039103, threshold=0.058655, 剪枝 2044989/2359296 (86.68%) 参数
2025-07-31 15:52:45,429 - __main__ - INFO - 层 bert.encoder.layer.11.output.dense: std=0.035710, threshold=0.053565, 剪枝 2051336/2359296 (86.95%) 参数
2025-07-31 15:52:45,430 - __main__ - INFO - 层 bert.pooler.dense: std=0.028567, threshold=0.042850, 剪枝 535271/589824 (90.75%) 参数
2025-07-31 15:52:45,431 - __main__ - INFO - 层 classifier: std=0.020092, threshold=0.030138, 剪枝 2643/3072 (86.04%) 参数
2025-07-31 15:52:45,431 - __main__ - INFO - 总体剪枝: 74588423/85527552 (87.21%) 参数
2025-07-31 15:52:45,432 - __main__ - INFO - 压缩比: 7.82x
2025-07-31 15:52:45,439 - __main__ - INFO - 
=== 剪枝统计 ===
2025-07-31 15:52:45,439 - __main__ - INFO - 总参数数: 85,527,552
2025-07-31 15:52:45,440 - __main__ - INFO - 剪枝参数数: 74,588,423
2025-07-31 15:52:45,440 - __main__ - INFO - 剪枝比例: 87.21%
2025-07-31 15:52:45,440 - __main__ - INFO - 压缩比: 7.82x
2025-07-31 15:52:45,441 - __main__ - INFO - 
=== 评估剪枝后模型 ===
2025-07-31 15:52:58,636 - __main__ - INFO - 验证准确率: 0.2821 (2144/7600)
2025-07-31 15:52:58,637 - __main__ - INFO - 验证损失: 1.3532
2025-07-31 15:52:59,656 - __main__ - INFO - 剪枝后模型已保存到: /download/model/pruned_model.pt
2025-07-31 15:52:59,656 - __main__ - INFO - 
=== 开始微调 (3 epochs) ===
2025-07-31 16:03:59,063 - __main__ - INFO - Epoch 1/3, 平均训练损失: 0.2522
2025-07-31 16:04:11,987 - __main__ - INFO - 验证准确率: 0.9257 (7035/7600)
2025-07-31 16:04:11,987 - __main__ - INFO - 验证损失: 0.2224
2025-07-31 16:15:07,835 - __main__ - INFO - Epoch 2/3, 平均训练损失: 0.1562
2025-07-31 16:15:20,818 - __main__ - INFO - 验证准确率: 0.9280 (7053/7600)
2025-07-31 16:15:20,818 - __main__ - INFO - 验证损失: 0.2106
2025-07-31 16:26:16,487 - __main__ - INFO - Epoch 3/3, 平均训练损失: 0.1170
2025-07-31 16:26:29,447 - __main__ - INFO - 验证准确率: 0.9296 (7065/7600)
2025-07-31 16:26:29,449 - __main__ - INFO - 验证损失: 0.2211
2025-07-31 16:26:29,450 - __main__ - INFO - 
=== 评估微调后模型 ===
2025-07-31 16:26:42,361 - __main__ - INFO - 验证准确率: 0.9296 (7065/7600)
2025-07-31 16:26:42,362 - __main__ - INFO - 验证损失: 0.2211
2025-07-31 16:26:43,527 - __main__ - INFO - 最终模型已保存到: /download/model/final_model.pt
2025-07-31 16:26:43,529 - __main__ - INFO - 结果摘要已保存到: /download/other/results_summary.txt
2025-07-31 16:26:43,530 - __main__ - INFO - 
==================================================
2025-07-31 16:26:43,530 - __main__ - INFO - 最终结果总结:
2025-07-31 16:26:43,530 - __main__ - INFO - 原始模型准确率: 0.9514
2025-07-31 16:26:43,531 - __main__ - INFO - 剪枝后准确率: 0.2821
2025-07-31 16:26:43,531 - __main__ - INFO - 微调后准确率: 0.9296
2025-07-31 16:26:43,531 - __main__ - INFO - 准确率下降: 0.0218 (2.30%)
2025-07-31 16:26:43,532 - __main__ - INFO - 模型压缩比: 7.82x
2025-07-31 16:26:43,532 - __main__ - INFO - 剪枝比例: 87.21%
2025-07-31 16:26:43,532 - __main__ - INFO - ==================================================
2025-07-31 16:26:43,533 - __main__ - INFO - 
📁 保存的文件:
2025-07-31 16:26:43,533 - __main__ - INFO -    - 原始模型: /download/model/original_model.pt
2025-07-31 16:26:43,533 - __main__ - INFO -    - 剪枝后模型: /download/model/pruned_model.pt
2025-07-31 16:26:43,534 - __main__ - INFO -    - 最终模型: /download/model/final_model.pt
2025-07-31 16:26:43,534 - __main__ - INFO -    - 详细日志: /download/other/bert_pruning.log
2025-07-31 16:26:43,535 - __main__ - INFO -    - 结果摘要: /download/other/results_summary.txt
2025-07-31 16:26:43,535 - __main__ - INFO - ==================================================
