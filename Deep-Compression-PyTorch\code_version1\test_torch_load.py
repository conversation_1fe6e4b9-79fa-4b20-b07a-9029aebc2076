#!/usr/bin/env python3
"""
测试torch.load兼容性的小脚本
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from bert_huffman_encode import safe_torch_load

def test_load():
    """测试加载8位量化模型"""
    model_path = "/download/model/quantized_model_8bit.pt"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    try:
        print("🔄 测试加载8位量化模型...")
        quantized_data = safe_torch_load(model_path, map_location='cpu')
        
        print("✅ 加载成功!")
        print(f"📊 数据键: {list(quantized_data.keys())}")
        print(f"📊 量化层数: {len(quantized_data.get('quantization_info', {}))}")
        print(f"📊 非量化参数数: {len(quantized_data.get('non_quantized_params', {}))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_load()
    if success:
        print("🎉 torch.load兼容性测试通过!")
    else:
        print("❌ torch.load兼容性测试失败!")
        sys.exit(1)