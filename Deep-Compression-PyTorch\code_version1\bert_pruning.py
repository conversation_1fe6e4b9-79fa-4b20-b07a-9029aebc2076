"""
BERT Model Pruning Script
基于Deep Compression论文的剪枝算法，专门针对BERT模型进行结构化剪枝

主要功能：
1. 加载预训练的BERT模型 (textattack/bert-base-uncased-ag-news)
2. 实现权重剪枝（按百分位数或标准差）
3. 支持剪枝后的微调训练
4. 提供详细的剪枝统计信息

使用方法：
python bert_pruning.py --epochs 3 --sensitivity 0.25 --prune_method std
"""

import argparse
import os
import warnings
from typing import Dict, Any, List, Tuple
import struct
import pickle

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from transformers import TrainingArguments, Trainer
from datasets import load_dataset, Dataset
from tqdm import tqdm
import logging

# 忽略一些警告
warnings.filterwarnings("ignore", category=UserWarning)

# 文件保存目录配置（适配Modal平台）
SAVE_DIRS = {
    'model': '/download/model',
    'image': '/download/image', 
    'other': '/download/other'
}

def setup_save_directories():
    """创建保存文件所需的目录结构"""
    for dir_type, dir_path in SAVE_DIRS.items():
        os.makedirs(dir_path, exist_ok=True)
        print(f"✓ {dir_type.capitalize()} directory ready: {dir_path}")

def get_save_path(filename, file_type='other'):
    """获取文件的完整保存路径"""
    if file_type not in SAVE_DIRS:
        file_type = 'other'
    return os.path.join(SAVE_DIRS[file_type], filename)

# 创建保存目录
setup_save_directories()

# 设置日志（适配Modal平台）
def setup_logging():
    """设置日志配置，同时输出到控制台和文件 TODO"""
    log_file = get_save_path('bert_pruning_v7.log', 'other')
    
    # 创建日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 设置根日志器
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    # 清除已有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    logger.info(f"日志文件保存到: {log_file}")
    return logger

logger = setup_logging()


class SparseMatrix:
    """
    稀疏矩阵存储类，实现CSR格式和索引差压缩
    基于Deep Compression论文的实现
    """
    
    def __init__(self, dense_matrix: torch.Tensor, layer_type: str = 'fc'):
        """
        Args:
            dense_matrix: 稠密矩阵 (torch.Tensor)
            layer_type: 层类型 ('fc' for fully connected, 'conv' for convolutional)
        """
        self.original_shape = dense_matrix.shape
        self.layer_type = layer_type
        self.dtype = dense_matrix.dtype
        self.device = dense_matrix.device
        
        # 转换为CSR格式
        self._convert_to_csr(dense_matrix)
        
        # 压缩索引差
        self._compress_indices()
    
    def _convert_to_csr(self, matrix: torch.Tensor):
        """将稠密矩阵转换为CSR格式"""
        # 转为2D矩阵
        if len(matrix.shape) > 2:
            matrix = matrix.view(matrix.shape[0], -1)
        
        # 转为numpy进行CSR转换
        matrix_np = matrix.cpu().numpy()
        
        # 找到非零元素
        rows, cols = np.nonzero(matrix_np)
        values = matrix_np[rows, cols]
        
        # 构建CSR格式
        self.values = values.astype(np.float32)  # 非零值数组
        self.col_indices = cols.astype(np.int32)  # 列索引数组
        
        # 构建行指针数组
        self.row_pointers = np.zeros(matrix_np.shape[0] + 1, dtype=np.int32)
        for i in range(len(rows)):
            self.row_pointers[rows[i] + 1] += 1
        
        # 累积求和得到行指针
        for i in range(1, len(self.row_pointers)):
            self.row_pointers[i] += self.row_pointers[i - 1]
        
        self.num_rows = matrix_np.shape[0]
        self.num_cols = matrix_np.shape[1]
        self.nnz = len(values)  # 非零元素数量
        
        logger.debug(f"CSR转换: 原始大小={matrix.numel()}, 非零元素={self.nnz}, 稀疏度={1-self.nnz/matrix.numel():.4f}")
    
    def _compress_indices(self):
        """使用真正的稀疏存储，不保存重复数据"""
        if len(self.col_indices) == 0:
            self.index_metadata = {'method': 'empty'}
            return
        
        # 真正的稀疏存储：只保留必要的CSR格式数据
        # 不需要额外的压缩，CSR本身就很高效
        self.index_metadata = {
            'method': 'csr_only',
            'layer_type': self.layer_type,
            'nnz': len(self.col_indices)
        }
        
        # 计算压缩比
        original_size = len(self.col_indices) * 4  # int32
        # CSR实际存储：values + col_indices + row_pointers
        csr_size = (len(self.values) * 4 +  # values (float32)
                   len(self.col_indices) * 4 +  # col_indices (int32) 
                   len(self.row_pointers) * 4)  # row_pointers (int32)
        
        compression_ratio = (self.num_rows * self.num_cols * 4) / csr_size if csr_size > 0 else 1.0
        
        logger.debug(f"CSR存储: 原始{self.num_rows}x{self.num_cols} -> CSR({len(self.values)}非零), 压缩比={compression_ratio:.2f}x")
    
    def _decompress_indices(self):
        """CSR格式下直接返回列索引，无需额外解压缩"""
        return self.col_indices
    
    def to_dense(self) -> torch.Tensor:
        """将稀疏矩阵转换回稠密矩阵"""
        # 解压缩索引
        col_indices = self._decompress_indices()
        
        # 重建稠密矩阵
        dense = np.zeros((self.num_rows, self.num_cols), dtype=np.float32)
        
        value_idx = 0
        for row in range(self.num_rows):
            start_idx = self.row_pointers[row]
            end_idx = self.row_pointers[row + 1]
            
            for col_idx in range(start_idx, end_idx):
                if value_idx < len(self.values) and col_idx < len(col_indices):
                    dense[row, col_indices[col_idx]] = self.values[value_idx]
                    value_idx += 1
        
        # 恢复原始形状
        dense_tensor = torch.from_numpy(dense).to(self.device, dtype=self.dtype)
        if len(self.original_shape) > 2:
            dense_tensor = dense_tensor.view(self.original_shape)
        
        return dense_tensor
    
    def get_storage_size(self) -> Dict[str, int]:
        """获取CSR存储大小信息（字节）"""
        return {
            'values': self.values.nbytes,
            'col_indices': self.col_indices.nbytes,
            'row_pointers': self.row_pointers.nbytes,
            'metadata': len(pickle.dumps(self.index_metadata)),
            'total': (self.values.nbytes + self.col_indices.nbytes + 
                     self.row_pointers.nbytes + len(pickle.dumps(self.index_metadata)))
        }
    
    def get_compression_ratio(self, original_size: int) -> float:
        """计算相对于原始稠密矩阵的压缩比"""
        sparse_size = self.get_storage_size()['total']
        return original_size / sparse_size if sparse_size > 0 else 1.0


class BERTPruningModule:
    """
    BERT模型剪枝的主要类
    
    这个类包装了BERT模型，提供了剪枝功能，包括：
    - 按百分位数剪枝
    - 按标准差剪枝
    - 真正的稀疏矩阵存储(CSR格式)
    - 索引差压缩
    """
    
    def __init__(self, model: nn.Module):
        """
        初始化剪枝模块
        
        Args:
            model: BERT模型实例
        """
        self.model = model
        self.sparse_weights = {}  # 存储稀疏权重
        self.original_weights = {}  # 备份原始权重用于统计
        self.pruned_layers = set()  # 已剪枝的层
        self._backup_original_weights()
    
    def _backup_original_weights(self):
        """备份原始权重用于统计"""
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear) and 'bias' not in name:
                self.original_weights[name] = module.weight.data.clone()
    
    def _determine_layer_type(self, layer_name: str) -> str:
        """根据层名称确定层类型"""
        # BERT的全连接层通常包含这些关键词
        if any(keyword in layer_name.lower() for keyword in 
               ['dense', 'query', 'key', 'value', 'output', 'classifier', 'pooler']):
            return 'fc'
        else:
            return 'conv'  # 默认为卷积层
    
    def prune_by_percentile(self, percentile: float = 5.0, exclude_layers: List[str] = None):
        """
        按百分位数进行剪枝，实现真正的稀疏存储
        
        Args:
            percentile: 剪枝百分位数（0-100），例如5.0表示剪枝最小的5%权重
            exclude_layers: 要排除的层名称列表
        """
        if exclude_layers is None:
            exclude_layers = []
        
        # 收集所有可剪枝的权重
        all_weights = []
        for name, module in self.model.named_modules():
            if (isinstance(module, nn.Linear) and 
                name not in exclude_layers and 
                'bias' not in name):
                
                weight = module.weight.data.cpu().numpy().flatten()
                # 只考虑非零权重（避免重复剪枝已经为0的权重）
                nonzero_weights = weight[weight != 0]
                if len(nonzero_weights) > 0:
                    all_weights.append(nonzero_weights)
        
        if not all_weights:
            logger.warning("没有找到可剪枝的权重")
            return
        
        # 计算全局阈值
        all_weights_concat = np.concatenate(all_weights)
        threshold = np.percentile(np.abs(all_weights_concat), percentile)
        
        logger.info(f'使用阈值进行剪枝: {threshold:.6f}')
        
        # 应用剪枝并转换为稀疏格式
        total_params = 0
        pruned_params = 0
        total_original_size = 0
        total_sparse_size = 0
        
        for name, module in self.model.named_modules():
            if (isinstance(module, nn.Linear) and 
                name not in exclude_layers and 
                'bias' not in name):
                
                weight = module.weight.data
                
                # 应用阈值，创建稀疏权重
                sparse_weight = weight.clone()
                mask = torch.abs(weight) >= threshold
                sparse_weight[~mask] = 0
                
                # 转换为稀疏矩阵存储
                layer_type = self._determine_layer_type(name)
                sparse_matrix = SparseMatrix(sparse_weight, layer_type)
                
                # 存储稀疏矩阵
                self.sparse_weights[name] = sparse_matrix
                self.pruned_layers.add(name)
                
                # 用稀疏矩阵重建的稠密矩阵替换原权重
                module.weight.data = sparse_matrix.to_dense()
                
                # 统计信息
                total_params += weight.numel()
                pruned_params += (~mask).sum().item()
                
                # 计算存储大小
                original_size = weight.numel() * 4  # float32
                sparse_size = sparse_matrix.get_storage_size()['total']
                total_original_size += original_size
                total_sparse_size += sparse_size
                
                sparsity = (~mask).sum().item() / weight.numel()
                storage_compression = original_size / sparse_size if sparse_size > 0 else 1.0
                
                logger.info(f'层 {name}: 剪枝 {(~mask).sum().item()}/{weight.numel()} '
                           f'({100 * sparsity:.2f}%) 参数, '
                           f'存储压缩 {storage_compression:.2f}x')
        
        # 总体统计
        param_compression_ratio = total_params / (total_params - pruned_params) if pruned_params < total_params else 1.0
        storage_compression_ratio = total_original_size / total_sparse_size if total_sparse_size > 0 else 1.0
        
        logger.info(f'总体剪枝: {pruned_params}/{total_params} '
                   f'({100 * pruned_params / total_params:.2f}%) 参数')
        logger.info(f'参数压缩比: {param_compression_ratio:.2f}x')
        logger.info(f'存储压缩比: {storage_compression_ratio:.2f}x (包含索引压缩)')
    
    def prune_by_std(self, sensitivity: float = 0.25, exclude_layers: List[str] = None):
        """
        按标准差进行剪枝，实现真正的稀疏存储
        
        Args:
            sensitivity: 敏感度参数，阈值 = std * sensitivity
            exclude_layers: 要排除的层名称列表
        """
        if exclude_layers is None:
            exclude_layers = []
        
        total_params = 0
        pruned_params = 0
        total_original_size = 0
        total_sparse_size = 0
        
        for name, module in self.model.named_modules():
            if (isinstance(module, nn.Linear) and 
                name not in exclude_layers and 
                'bias' not in name):
                
                weight = module.weight.data
                std = torch.std(weight).item()
                threshold = std * sensitivity
                
                # 应用阈值，创建稀疏权重
                sparse_weight = weight.clone()
                mask = torch.abs(weight) >= threshold
                sparse_weight[~mask] = 0
                
                # 转换为稀疏矩阵存储
                layer_type = self._determine_layer_type(name)
                sparse_matrix = SparseMatrix(sparse_weight, layer_type)
                
                # 存储稀疏矩阵
                self.sparse_weights[name] = sparse_matrix
                self.pruned_layers.add(name)
                
                # 用稀疏矩阵重建的稠密矩阵替换原权重
                module.weight.data = sparse_matrix.to_dense()
                
                # 统计信息
                total_params += weight.numel()
                pruned_params += (~mask).sum().item()
                
                # 计算存储大小
                original_size = weight.numel() * 4  # float32
                sparse_size = sparse_matrix.get_storage_size()['total']
                total_original_size += original_size
                total_sparse_size += sparse_size
                
                sparsity = (~mask).sum().item() / weight.numel()
                storage_compression = original_size / sparse_size if sparse_size > 0 else 1.0
                
                logger.info(f'层 {name}: std={std:.6f}, threshold={threshold:.6f}, '
                           f'剪枝 {(~mask).sum().item()}/{weight.numel()} '
                           f'({100 * sparsity:.2f}%) 参数, '
                           f'存储压缩 {storage_compression:.2f}x')
        
        # 总体统计
        param_compression_ratio = total_params / (total_params - pruned_params) if pruned_params < total_params else 1.0
        storage_compression_ratio = total_original_size / total_sparse_size if total_sparse_size > 0 else 1.0
        
        logger.info(f'总体剪枝: {pruned_params}/{total_params} '
                   f'({100 * pruned_params / total_params:.2f}%) 参数')
        logger.info(f'参数压缩比: {param_compression_ratio:.2f}x')
        logger.info(f'存储压缩比: {storage_compression_ratio:.2f}x (包含索引压缩)')
    
    def get_pruning_stats(self) -> Dict[str, Any]:
        """
        获取剪枝统计信息，包括存储压缩比
        
        Returns:
            包含剪枝统计的字典
        """
        stats = {
            'layer_stats': {},
            'total_params': 0,
            'pruned_params': 0,
            'param_compression_ratio': 1.0,
            'storage_compression_ratio': 1.0,
            'total_original_size': 0,
            'total_sparse_size': 0
        }
        
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear) and 'bias' not in name:
                weight = module.weight.data
                total = weight.numel()
                original_size = total * 4  # float32
                
                stats['total_params'] += total
                stats['total_original_size'] += original_size
                
                if name in self.sparse_weights:
                    # 已剪枝的层
                    sparse_matrix = self.sparse_weights[name]
                    sparse_size = sparse_matrix.get_storage_size()['total']
                    pruned = total - sparse_matrix.nnz
                    
                    stats['layer_stats'][name] = {
                        'total_params': total,
                        'pruned_params': pruned,
                        'remaining_params': sparse_matrix.nnz,
                        'pruning_ratio': pruned / total if total > 0 else 0,
                        'shape': list(weight.shape),
                        'original_size_bytes': original_size,
                        'sparse_size_bytes': sparse_size,
                        'storage_compression': original_size / sparse_size if sparse_size > 0 else 1.0,
                        'sparsity': 1 - sparse_matrix.nnz / total if total > 0 else 0
                    }
                    
                    stats['pruned_params'] += pruned
                    stats['total_sparse_size'] += sparse_size
                else:
                    # 未剪枝的层
                    stats['layer_stats'][name] = {
                        'total_params': total,
                        'pruned_params': 0,
                        'remaining_params': total,
                        'pruning_ratio': 0.0,
                        'shape': list(weight.shape),
                        'original_size_bytes': original_size,
                        'sparse_size_bytes': original_size,  # 未压缩
                        'storage_compression': 1.0,
                        'sparsity': 0.0
                    }
                    
                    stats['total_sparse_size'] += original_size
        
        # 计算总体压缩比
        if stats['pruned_params'] < stats['total_params']:
            stats['param_compression_ratio'] = stats['total_params'] / (stats['total_params'] - stats['pruned_params'])
        
        if stats['total_sparse_size'] > 0:
            stats['storage_compression_ratio'] = stats['total_original_size'] / stats['total_sparse_size']
        
        return stats
    
    def save_sparse_model(self, filepath: str):
        """
        保存稀疏模型，使用PyTorch原生格式避免序列化问题
        
        Args:
            filepath: 保存路径
        """
        # 将稀疏权重转换为极简格式：只保存非零值和位置
        sparse_data = {}
        for layer_name, sparse_matrix in self.sparse_weights.items():
            # 从原始密集张量获取非零值和位置
            dense_tensor = sparse_matrix.to_dense()
            nonzero_mask = dense_tensor != 0
            nonzero_indices = torch.nonzero(nonzero_mask, as_tuple=False)
            nonzero_values = dense_tensor[nonzero_mask]
            
            # 极简格式：只保存必要数据
            sparse_data[layer_name] = {
                'nonzero_indices': nonzero_indices.cpu(),  # 非零位置 (N x 2)
                'nonzero_values': nonzero_values.cpu(),    # 非零值 (N,)
                'shape': dense_tensor.shape,               # 原始形状
                'dtype': dense_tensor.dtype,               # 数据类型
                'nnz': len(nonzero_values)                 # 非零元素数量
            }
        
        # 保存为标准PyTorch格式
        model_data = {
            'sparse_data': sparse_data,  # 极简稀疏数据
            'pruned_layers': list(self.pruned_layers),  # 转换为list
            'model_config': getattr(self.model, 'config', None),
            'sparse_format_version': '2.0'  # 新版本格式
        }
        
        # 只保存关键的非Linear权重参数 (embedding, classifier等)
        essential_params = {}
        for name, param in self.model.named_parameters():
            # 只保存真正必要的参数，跳过Linear层的权重和bias
            layer_name = '.'.join(name.split('.')[:-1])
            is_linear_layer = any(isinstance(module, nn.Linear) 
                                for n, module in self.model.named_modules() 
                                if n == layer_name)
            
            if not is_linear_layer or 'bias' in name:
                # 非Linear层的参数或Linear层的bias
                essential_params[name] = param.data.clone()
        
        model_data['essential_params'] = essential_params
        
        # 使用weights_only=False保存，但结构简单
        torch.save(model_data, filepath)
        
        # 计算压缩效果
        file_size = os.path.getsize(filepath) / 1024 / 1024  # MB
        
        # 计算原始模型大小（只计算Linear层的权重）
        original_size_bytes = 0
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear):
                original_size_bytes += module.weight.numel() * 4  # FP32
        original_size = original_size_bytes / 1024 / 1024  # MB
        
        compression_ratio = original_size / file_size if file_size > 0 else 1.0
        
        logger.info(f"稀疏模型已保存: {filepath}")
        logger.info(f"文件大小: {file_size:.2f} MB (线性层参考: {original_size:.2f} MB)")
        logger.info(f"文件压缩比: {compression_ratio:.2f}x")
    
    def load_sparse_model(self, filepath: str, model: nn.Module):
        """
        加载稀疏模型，支持新的PyTorch原生格式
        
        Args:
            filepath: 模型文件路径
            model: 目标模型实例
        """
        # 使用weights_only=False加载自定义数据结构
        model_data = torch.load(filepath, map_location='cpu', weights_only=False)
        
        format_version = model_data.get('sparse_format_version', '1.0')
        
        if format_version == '2.0':
            # 新格式：从极简稀疏数据重建
            sparse_data_dict = model_data.get('sparse_data', model_data.get('sparse_tensors', {}))
            self.pruned_layers = set(model_data['pruned_layers'])
            
            # 重建剪枝层的权重
            for name, module in model.named_modules():
                if isinstance(module, nn.Linear) and name in sparse_data_dict:
                    # 从极简格式重建密集权重
                    layer_data = sparse_data_dict[name]
                    
                    if 'nonzero_indices' in layer_data:
                        # 新的极简格式
                        shape = layer_data['shape']
                        dtype = layer_data['dtype']
                        nonzero_indices = layer_data['nonzero_indices']
                        nonzero_values = layer_data['nonzero_values']
                        
                        # 创建零张量并填充非零值
                        dense_weight = torch.zeros(shape, dtype=dtype)
                        if len(nonzero_indices) > 0:
                            # 使用高级索引设置非零值
                            dense_weight[nonzero_indices[:, 0], nonzero_indices[:, 1]] = nonzero_values
                        
                        module.weight.data = dense_weight
                        logger.debug(f"重建剪枝层 {name}: {len(nonzero_values)}/{shape[0] * shape[1]}")
                    else:
                        # 兼容COO格式
                        indices = layer_data['indices']
                        values = layer_data['values']
                        shape = layer_data['shape']
                        
                        sparse_tensor = torch.sparse_coo_tensor(
                            indices, values, shape, 
                            dtype=layer_data['dtype']
                        ).coalesce()
                        
                        dense_weight = sparse_tensor.to_dense()
                        module.weight.data = dense_weight
                        logger.debug(f"重建剪枝层 {name}: {sparse_tensor._nnz()}/{shape[0] * shape[1]}")
        else:
            # 兼容旧格式（如果存在）
            self.sparse_weights = model_data.get('sparse_weights', {})
            self.pruned_layers = set(model_data.get('pruned_layers', []))
            
            for name, module in model.named_modules():
                if isinstance(module, nn.Linear) and name in self.sparse_weights:
                    sparse_matrix = self.sparse_weights[name]
                    module.weight.data = sparse_matrix.to_dense()
        
        # 加载必要的参数
        if 'essential_params' in model_data:
            for param_name, param_data in model_data['essential_params'].items():
                try:
                    # 通过参数名设置参数
                    names = param_name.split('.')
                    current = model
                    for name in names[:-1]:
                        current = getattr(current, name)
                    setattr(current, names[-1], nn.Parameter(param_data))
                    logger.debug(f"加载参数 {param_name}: {param_data.shape}")
                except Exception as e:
                    logger.warning(f"无法设置参数 {param_name}: {e}")
        
        logger.info(f"稀疏模型已加载: {filepath}")
        logger.info(f"格式版本: {format_version}")
        logger.info(f"剪枝层数: {len(self.pruned_layers)}")
        logger.info(f"加载的必要参数数: {len(model_data.get('essential_params', {}))}")
        
        return model


def create_data_loader(tokenizer, batch_size: int = 16, max_samples: int = None):
    """
    创建AG News数据集的数据加载器
    
    Args:
        tokenizer: BERT tokenizer
        batch_size: 批次大小
        max_samples: 最大样本数（用于快速测试）
        
    Returns:
        train_loader, val_loader
    """
    # 加载AG News数据集
    dataset = load_dataset("ag_news")
    
    def tokenize_function(examples):
        # 不在这里padding，而是在DataCollator中动态padding
        return tokenizer(examples["text"], truncation=True, max_length=512)
    
    # 如果指定了最大样本数，则截取
    if max_samples:
        train_dataset = dataset["train"].select(range(min(max_samples, len(dataset["train"]))))
        test_dataset = dataset["test"].select(range(min(max_samples//4, len(dataset["test"]))))
    else:
        train_dataset = dataset["train"]
        test_dataset = dataset["test"]
    
    # 应用tokenization
    train_dataset = train_dataset.map(tokenize_function, batched=True)
    test_dataset = test_dataset.map(tokenize_function, batched=True)
    
    # 设置format
    train_dataset.set_format(type="torch", columns=["input_ids", "attention_mask", "label"])
    test_dataset.set_format(type="torch", columns=["input_ids", "attention_mask", "label"])
    
    # 自定义collate函数，确保正确的padding
    def collate_fn(batch):
        """自定义的collate函数，处理变长序列"""
        # 提取各个字段
        input_ids = [item['input_ids'] for item in batch]
        attention_masks = [item['attention_mask'] for item in batch]
        labels = [item['label'] for item in batch]
        
        # Padding到批次中的最大长度
        max_len = max(len(ids) for ids in input_ids)
        
        # Pad input_ids和attention_masks
        padded_input_ids = []
        padded_attention_masks = []
        
        for ids, mask in zip(input_ids, attention_masks):
            # 计算需要padding的长度
            pad_len = max_len - len(ids)
            
            # Padding input_ids（使用tokenizer的pad_token_id）
            padded_ids = torch.cat([ids, torch.full((pad_len,), tokenizer.pad_token_id, dtype=ids.dtype)])
            padded_input_ids.append(padded_ids)
            
            # Padding attention_mask（使用0）
            padded_mask = torch.cat([mask, torch.zeros(pad_len, dtype=mask.dtype)])
            padded_attention_masks.append(padded_mask)
        
        # 堆叠成批次
        return {
            'input_ids': torch.stack(padded_input_ids),
            'attention_mask': torch.stack(padded_attention_masks),
            'label': torch.stack(labels)
        }
    
    # 创建数据加载器，使用自定义collate function
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        collate_fn=collate_fn
    )
    val_loader = torch.utils.data.DataLoader(
        test_dataset, 
        batch_size=batch_size, 
        shuffle=False,
        collate_fn=collate_fn
    )
    
    return train_loader, val_loader


def evaluate_model(model, val_loader, device):
    """
    评估模型性能
    
    Args:
        model: 要评估的模型
        val_loader: 验证数据加载器
        device: 设备
        
    Returns:
        accuracy: 准确率
    """
    model.eval()
    total_correct = 0
    total_samples = 0
    total_loss = 0
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc="评估中"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['label'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
            loss = outputs.loss
            logits = outputs.logits
            
            predictions = torch.argmax(logits, dim=-1)
            total_correct += (predictions == labels).sum().item()
            total_samples += labels.size(0)
            total_loss += loss.item()
    
    accuracy = total_correct / total_samples
    avg_loss = total_loss / len(val_loader)
    
    logger.info(f'验证准确率: {accuracy:.4f} ({total_correct}/{total_samples})')
    logger.info(f'验证损失: {avg_loss:.4f}')
    
    return accuracy


def fine_tune_model(model, train_loader, val_loader, device, pruning_module, epochs: int = 3, learning_rate: float = 2e-5):
    """
    对剪枝后的模型进行微调
    
    Args:
        model: 要微调的模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        device: 设备
        pruning_module: 剪枝模块实例，用于管理掩码
        epochs: 训练轮数
        learning_rate: 学习率
    """
    model.train()
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
    
    for epoch in range(epochs):
        total_loss = 0
        model.train()
        
        progress_bar = tqdm(train_loader, desc=f"训练 Epoch {epoch + 1}/{epochs}")
        
        for batch in progress_bar:
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['label'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
            loss = outputs.loss
            
            loss.backward()
            
            # 确保剪枝的权重梯度为0（重要！）
            for name, module in model.named_modules():
                if isinstance(module, nn.Linear) and name in pruning_module.sparse_weights:
                    if module.weight.grad is not None:
                        # 获取稀疏矩阵的掩码
                        sparse_matrix = pruning_module.sparse_weights[name]
                        # 创建掩码：非零位置为1，零位置为0
                        weight_data = module.weight.data
                        mask = (weight_data != 0).float().to(device)
                        # 将剪枝位置的梯度设为0
                        module.weight.grad.data *= mask
            
            optimizer.step()
            
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_loss = total_loss / len(train_loader)
        logger.info(f'Epoch {epoch + 1}/{epochs}, 平均训练损失: {avg_loss:.4f}')
        
        # 每个epoch后评估
        accuracy = evaluate_model(model, val_loader, device)


def print_model_info(model):
    """打印模型信息"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"模型总参数数: {total_params:,}")
    logger.info(f"可训练参数数: {trainable_params:,}")
    
    logger.info("\n模型结构概览:")
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear):
            logger.info(f"  {name}: {module.weight.shape}")

def save_results_summary(original_accuracy, pruned_accuracy, final_accuracy, stats, args):
    """保存结果摘要到文件 TODO"""
    summary_file = get_save_path('results_summary_v7.txt', 'other')
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("BERT模型剪枝结果摘要\n")
        f.write("=" * 50 + "\n\n")
        
        # 实验配置
        f.write("实验配置:\n")
        f.write(f"- 剪枝方法: {args.prune_method}\n")
        if args.prune_method == 'std':
            f.write(f"- 敏感度: {args.sensitivity}\n")
        else:
            f.write(f"- 百分位数: {args.percentile}\n")
        f.write(f"- 微调轮数: {args.epochs}\n")
        f.write(f"- 学习率: {args.learning_rate}\n")
        f.write(f"- 批次大小: {args.batch_size}\n")
        f.write(f"- 最大样本数: {args.max_samples or '全部'}\n\n")
        
        # 准确率结果
        f.write("准确率结果:\n")
        f.write(f"- 原始模型准确率: {original_accuracy:.4f}\n")
        f.write(f"- 剪枝后准确率: {pruned_accuracy:.4f}\n")
        f.write(f"- 微调后准确率: {final_accuracy:.4f}\n")
        f.write(f"- 准确率下降: {(original_accuracy - final_accuracy):.4f} "
               f"({100 * (original_accuracy - final_accuracy) / original_accuracy:.2f}%)\n\n")
        
        # 压缩统计
        f.write("压缩统计:\n")
        f.write(f"- 总参数数: {stats['total_params']:,}\n")
        f.write(f"- 剪枝参数数: {stats['pruned_params']:,}\n")
        f.write(f"- 剪枝比例: {100 * stats['pruned_params'] / stats['total_params']:.2f}%\n")
        f.write(f"- 参数压缩比: {stats['param_compression_ratio']:.2f}x\n")
        f.write(f"- 存储压缩比: {stats['storage_compression_ratio']:.2f}x\n\n")
        
        # 模型文件路径 TODO
        f.write("保存的模型文件:\n")
        f.write(f"- 原始模型: {get_save_path('original_model_v7.pt', 'model')}\n")
        f.write(f"- 剪枝后模型: {get_save_path('pruned_model_v7.pt', 'model')}\n")
        f.write(f"- 最终模型: {get_save_path('final_model_v7.pt', 'model')}\n\n")
        
        # 层级详细统计（前20层）
        f.write("层级剪枝详情（前20层）:\n")
        f.write(f"{'层名称':<40} {'剪枝比例':<10} {'剪枝参数':<12} {'总参数':<12}\n")
        f.write("-" * 80 + "\n")
        
        sorted_layers = sorted(stats['layer_stats'].items(), 
                              key=lambda x: x[1]['pruning_ratio'], 
                              reverse=True)
        
        for layer_name, layer_info in sorted_layers[:20]:
            f.write(f"{layer_name:<40} "
                   f"{100 * layer_info['pruning_ratio']:<10.2f}% "
                   f"{layer_info['pruned_params']:<12,} "
                   f"{layer_info['total_params']:<12,}\n")
    
    logger.info(f"结果摘要已保存到: {summary_file}")
    return summary_file


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='BERT模型剪枝脚本')
    parser.add_argument('--model_name', type=str, default='textattack/bert-base-uncased-ag-news',
                        help='要加载的BERT模型名称')
    parser.add_argument('--batch_size', type=int, default=16,
                        help='批次大小 (默认: 16)')
    parser.add_argument('--epochs', type=int, default=3,
                        help='微调的训练轮数 (默认: 3)')
    parser.add_argument('--learning_rate', type=float, default=2e-5,
                        help='学习率 (默认: 2e-5)')
    parser.add_argument('--prune_method', type=str, choices=['percentile', 'std'], default='std',
                        help='剪枝方法: percentile 或 std (默认: std)')
    parser.add_argument('--percentile', type=float, default=5.0,
                        help='百分位数剪枝的百分位数 (默认: 5.0)')
    parser.add_argument('--sensitivity', type=float, default=0.25,
                        help='标准差剪枝的敏感度参数 (默认: 0.25)')
    parser.add_argument('--max_samples', type=int, default=None,
                        help='最大样本数，用于快速测试 (默认: None，使用全部数据)')
    parser.add_argument('--no_cuda', action='store_true', default=False,
                        help='禁用CUDA')
    parser.add_argument('--save_path', type=str, default=None,
                        help='模型保存路径（已自动配置为Modal平台路径）')
    
    args = parser.parse_args()
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and not args.no_cuda else 'cpu')
    logger.info(f'使用设备: {device}')
    
    # 加载模型和tokenizer
    logger.info(f'加载模型: {args.model_name}')
    tokenizer = AutoTokenizer.from_pretrained(args.model_name)
    model = AutoModelForSequenceClassification.from_pretrained(args.model_name)
    model.to(device)
    
    # 打印模型信息
    logger.info("=== 原始模型信息 ===")
    print_model_info(model)
    
    # 创建数据加载器
    logger.info("创建数据加载器...")
    try:
        train_loader, val_loader = create_data_loader(tokenizer, args.batch_size, args.max_samples)
        logger.info(f"数据加载器创建成功，训练集批次数: {len(train_loader)}, 验证集批次数: {len(val_loader)}")
        
        # 测试数据加载器
        logger.info("测试数据加载器...")
        test_batch = next(iter(val_loader))
        logger.info(f"测试批次形状: input_ids={test_batch['input_ids'].shape}, "
                   f"attention_mask={test_batch['attention_mask'].shape}, "
                   f"labels={test_batch['label'].shape}")
    except Exception as e:
        logger.error(f"数据加载器创建失败: {e}")
        raise
    
    # 评估原始模型
    logger.info("\n=== 评估原始模型 ===")
    original_accuracy = evaluate_model(model, val_loader, device)
    
    # 保存原始模型
    original_save_path = get_save_path('original_model.pt', 'model')
    torch.save(model.state_dict(), original_save_path)
    logger.info(f'原始模型已保存到: {original_save_path}')
    
    # 创建剪枝模块
    logger.info("\n=== 开始剪枝 ===")
    pruning_module = BERTPruningModule(model)
    
    # 执行剪枝
    if args.prune_method == 'percentile':
        logger.info(f'使用百分位数剪枝，百分位数: {args.percentile}')
        pruning_module.prune_by_percentile(args.percentile)
    else:
        logger.info(f'使用标准差剪枝，敏感度: {args.sensitivity}')
        pruning_module.prune_by_std(args.sensitivity)
    
    # 获取剪枝统计
    stats = pruning_module.get_pruning_stats()
    logger.info(f"\n=== 剪枝统计 ===")
    logger.info(f"总参数数: {stats['total_params']:,}")
    logger.info(f"剪枝参数数: {stats['pruned_params']:,}")
    logger.info(f"剪枝比例: {100 * stats['pruned_params'] / stats['total_params']:.2f}%")
    logger.info(f"参数压缩比: {stats['param_compression_ratio']:.2f}x")
    logger.info(f"存储压缩比: {stats['storage_compression_ratio']:.2f}x")
    
    # 评估剪枝后的模型
    logger.info("\n=== 评估剪枝后模型 ===")
    pruned_accuracy = evaluate_model(model, val_loader, device)
    
    # 保存剪枝后的稀疏模型
    pruned_save_path = get_save_path('pruned_model.pt', 'model')
    pruning_module.save_sparse_model(pruned_save_path)
    logger.info(f'剪枝后稀疏模型已保存到: {pruned_save_path}')
    
    # 微调剪枝后的模型
    logger.info(f"\n=== 开始微调 ({args.epochs} epochs) ===")
    fine_tune_model(model, train_loader, val_loader, device, pruning_module, args.epochs, args.learning_rate)
    
    # 评估微调后的模型
    logger.info("\n=== 评估微调后模型 ===")
    final_accuracy = evaluate_model(model, val_loader, device)
    
    # 保存最终稀疏模型
    final_save_path = get_save_path('final_model.pt', 'model')
    pruning_module.save_sparse_model(final_save_path)
    logger.info(f'最终稀疏模型已保存到: {final_save_path}')
    
    # 保存详细结果摘要
    summary_file = save_results_summary(original_accuracy, pruned_accuracy, final_accuracy, stats, args)
    
    # 总结
    logger.info("\n" + "="*50)
    logger.info("最终结果总结:")
    logger.info(f"原始模型准确率: {original_accuracy:.4f}")
    logger.info(f"剪枝后准确率: {pruned_accuracy:.4f}")
    logger.info(f"微调后准确率: {final_accuracy:.4f}")
    logger.info(f"准确率下降: {(original_accuracy - final_accuracy):.4f} "
                f"({100 * (original_accuracy - final_accuracy) / original_accuracy:.2f}%)")
    logger.info(f"模型参数压缩比: {stats['param_compression_ratio']:.2f}x")
    logger.info(f"模型存储压缩比: {stats['storage_compression_ratio']:.2f}x")
    logger.info(f"剪枝比例: {100 * stats['pruned_params'] / stats['total_params']:.2f}%")
    logger.info("="*50)
    logger.info("\n📁 保存的文件:")
    logger.info(f"   - 原始模型: {get_save_path('original_model.pt', 'model')}")
    logger.info(f"   - 剪枝后模型: {get_save_path('pruned_model.pt', 'model')}")
    logger.info(f"   - 最终模型: {get_save_path('final_model.pt', 'model')}")
    logger.info(f"   - 详细日志: {get_save_path('bert_pruning.log', 'other')}")
    logger.info(f"   - 结果摘要: {summary_file}")
    logger.info("="*50)


if __name__ == '__main__':
    main() 