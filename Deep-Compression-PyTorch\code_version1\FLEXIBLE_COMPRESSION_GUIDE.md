# BERT模型灵活深度压缩指南

## 概述

这是一个基于Deep Compression论文的灵活BERT模型压缩管道，支持多种压缩方案的组合。适配PyTorch 2.6和Modal平台。

## 功能特性

### 🔧 支持的压缩方案
- **仅剪枝**: 移除不重要的权重连接
- **仅量化**: 将32位权重压缩到8位或其他位宽
- **仅哈夫曼编码**: 对模型进行熵编码压缩
- **剪枝+量化**: 先剪枝再量化
- **量化+哈夫曼编码**: 先量化再哈夫曼编码
- **剪枝+哈夫曼编码**: 先剪枝再哈夫曼编码
- **全套流程**: 剪枝→量化→哈夫曼编码

### 🏷️ 模型标记系统
- 自定义模型名称标记，用于区分不同实验
- 自动生成带时间戳的文件名
- 完整的日志记录和配置保存

### 📂 文件组织
```
/download/
├── model/                           # 模型文件目录
│   ├── {model_name}_original.pt    # 原始模型
│   ├── {model_name}_pruned.pt      # 剪枝后模型
│   ├── {model_name}_final.pt       # 微调后模型
│   ├── {model_name}_quantized.pt   # 量化模型(FP32格式)
│   ├── {model_name}_quantized_8bit.pt # 8位量化模型
│   └── {model_name}_huffman_*.tar.gz  # 哈夫曼编码压缩包
└── other/                          # 日志和结果文件
    ├── {model_name}_pipeline_*.log # 执行日志
    ├── {model_name}_pipeline_config_*.json # 配置文件
    └── {model_name}_compression_summary_*.txt # 压缩总结
```

## 使用方法

### 基本语法
```bash
python run_full_compression.py --model_name <标记名称> --steps <压缩步骤> [其他参数]
```

### 参数说明

#### 必需参数
- `--model_name`: 模型标记名称，用于区分不同的压缩实验
- `--steps`: 要执行的压缩步骤，可选值：
  - `pruning`: 仅剪枝
  - `quantization`: 仅量化
  - `huffman`: 仅哈夫曼编码
  - `pruning+quantization`: 剪枝+量化
  - `quantization+huffman`: 量化+哈夫曼编码
  - `pruning+huffman`: 剪枝+哈夫曼编码
  - `all`: 全套流程(剪枝+量化+哈夫曼编码)

#### 剪枝参数
- `--prune_method`: 剪枝方法 (`std` 或 `percentile`，默认: `std`)
- `--sensitivity`: 标准差剪枝敏感度 (默认: 0.25)
- `--percentile`: 百分位数剪枝百分位数 (默认: 5.0)
- `--epochs`: 微调轮数 (默认: 3)
- `--batch_size`: 批次大小 (默认: 16)
- `--max_samples`: 最大样本数，用于快速测试 (可选)

#### 量化参数
- `--num_bits`: 量化位数 (默认: 8)

#### 其他参数
- `--no_cuda`: 禁用CUDA

## 使用示例

### 1. 完整压缩流程
```bash
# 使用默认参数执行完整压缩
python run_full_compression.py --model_name experiment_v1 --steps all

# 自定义参数的完整压缩
python run_full_compression.py \
    --model_name high_compression \
    --steps all \
    --sensitivity 0.3 \
    --num_bits 4 \
    --epochs 5 \
    --max_samples 10000
```

### 2. 仅剪枝
```bash
# 标准差剪枝
python run_full_compression.py \
    --model_name pruning_only \
    --steps pruning \
    --prune_method std \
    --sensitivity 0.25 \
    --epochs 3

# 百分位数剪枝
python run_full_compression.py \
    --model_name percentile_pruning \
    --steps pruning \
    --prune_method percentile \
    --percentile 10.0
```

### 3. 仅量化
```bash
# 8位量化
python run_full_compression.py \
    --model_name quantization_8bit \
    --steps quantization \
    --num_bits 8

# 4位量化
python run_full_compression.py \
    --model_name quantization_4bit \
    --steps quantization \
    --num_bits 4
```

### 4. 仅哈夫曼编码
```bash
python run_full_compression.py \
    --model_name huffman_only \
    --steps huffman
```

### 5. 组合压缩方案
```bash
# 剪枝+量化
python run_full_compression.py \
    --model_name prune_quant \
    --steps pruning+quantization \
    --sensitivity 0.2 \
    --num_bits 8

# 量化+哈夫曼编码
python run_full_compression.py \
    --model_name quant_huffman \
    --steps quantization+huffman \
    --num_bits 6

# 剪枝+哈夫曼编码
python run_full_compression.py \
    --model_name prune_huffman \
    --steps pruning+huffman \
    --prune_method percentile \
    --percentile 5.0
```

### 6. 快速测试
```bash
# 使用少量样本进行快速测试
python run_full_compression.py \
    --model_name quick_test \
    --steps all \
    --max_samples 1000 \
    --epochs 1
```

## 输出文件说明

### 模型文件
- **原始模型**: `{model_name}_original.pt` - 预训练BERT模型
- **剪枝后模型**: `{model_name}_pruned.pt` - 剪枝但未微调的模型
- **微调后模型**: `{model_name}_final.pt` - 剪枝+微调后的模型
- **量化模型(FP32)**: `{model_name}_quantized.pt` - 兼容格式的量化模型
- **8位量化模型**: `{model_name}_quantized_8bit.pt` - 真正的8位压缩存储
- **哈夫曼压缩包**: `{model_name}_huffman_*.tar.gz` - 最终压缩包

### 日志和结果文件
- **执行日志**: `{model_name}_pipeline_*.log` - 详细的执行过程记录
- **配置文件**: `{model_name}_pipeline_config_*.json` - 实验配置参数
- **压缩总结**: `{model_name}_compression_summary_*.txt` - 可读的压缩效果报告

## 技术说明

### 剪枝方法
- **标准差剪枝**: 基于权重标准差的阈值剪枝
- **百分位数剪枝**: 按权重绝对值的百分位数剪枝

### 量化方法
- 使用K-means聚类进行权重共享
- 支持任意位宽量化(推荐4-8位)
- 保持稀疏性(零值权重)

### 哈夫曼编码
- 对量化索引和码表分别编码
- 自适应熵编码，无损压缩
- 支持模型重建和推理测试

## 注意事项

1. **PyTorch 2.6兼容性**: 代码已适配PyTorch 2.6的`weights_only`安全模式
2. **Modal平台优化**: 目录结构和路径适配Modal云平台
3. **内存使用**: 大模型压缩可能需要较大内存，建议使用GPU环境
4. **性能权衡**: 更高的压缩比通常伴随更多的性能损失
5. **文件管理**: 每次实验使用不同的`model_name`避免文件冲突

## 最佳实践

### 实验设计
1. **渐进式压缩**: 先单独测试各个压缩方法的效果
2. **参数调优**: 从保守参数开始，逐步增加压缩强度
3. **性能监控**: 关注压缩比和准确率的平衡

### 参数建议
- **初次尝试**: `--sensitivity 0.25 --num_bits 8`
- **中等压缩**: `--sensitivity 0.3 --num_bits 6`  
- **高压缩**: `--sensitivity 0.4 --num_bits 4`

### 故障排除
- 检查CUDA可用性和内存使用
- 查看详细日志文件定位问题
- 从较小的`max_samples`开始测试

## 结果分析

脚本会自动生成详细的压缩效果分析，包括：
- 各阶段模型大小对比
- 压缩比统计
- 性能影响评估
- 技术方法总结

通过这些信息可以评估不同压缩方案的效果，指导后续的优化方向。