#!/usr/bin/env python3
"""
BERT剪枝运行脚本 - Modal平台适配版
适配Modal云平台的特殊需求和配置

特点：
1. 使用Modal平台指定的文件保存路径
2. 自动保存所有重要文件到下载目录
3. 支持不同的运行模式和参数配置
4. 优化的日志和输出格式

使用示例：
1. 快速测试：
   python run_bert_pruning_modal.py --quick_test

2. 标准剪枝：
   python run_bert_pruning_modal.py --std_pruning --sensitivity 0.25

3. 百分位数剪枝：
   python run_bert_pruning_modal.py --percentile_pruning --percentile 10.0

4. 自定义配置：
   python run_bert_pruning_modal.py --custom --sensitivity 0.3 --epochs 5
"""

import subprocess
import argparse
import sys
import os
import time
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("🤖 BERT模型剪枝工具 - Modal平台版")
    print("基于Deep Compression论文的剪枝算法")
    print("=" * 70)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)

def print_mode_info(mode_name, description, estimated_time):
    """打印运行模式信息"""
    print(f"\n🚀 运行模式: {mode_name}")
    print(f"📝 描述: {description}")
    print(f"⏱️  预估时间: {estimated_time}")
    print("-" * 50)

def run_command(cmd):
    """运行命令并实时输出"""
    print(f"🔧 执行命令: {' '.join(cmd)}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # 使用实时输出
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时打印输出
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        elapsed_time = time.time() - start_time
        
        if process.returncode == 0:
            print(f"\n✅ 命令执行成功 (耗时: {elapsed_time:.1f}秒)")
            return True
        else:
            print(f"\n❌ 命令执行失败，返回码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"\n💥 执行过程中出现异常: {e}")
        return False

def check_saved_files():
    """检查保存的文件"""
    print("\n📁 检查保存的文件:")
    print("-" * 30)
    
    file_paths = [
        ('/download/model/original_model.pt', '原始模型'),
        ('/download/model/pruned_model.pt', '剪枝后模型'),
        ('/download/model/final_model.pt', '最终模型'),
        ('/download/other/bert_pruning.log', '详细日志'),
        ('/download/other/results_summary.txt', '结果摘要')
    ]
    
    for file_path, description in file_paths:
        if os.path.exists(file_path):
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"✅ {description}: {file_path} ({size_mb:.2f} MB)")
        else:
            print(f"❌ {description}: {file_path} (未找到)")

def print_summary():
    """打印运行总结"""
    print("\n" + "=" * 70)
    print("🎉 BERT剪枝任务完成！")
    print("=" * 70)
    
    # 尝试读取结果摘要
    summary_file = '/download/other/results_summary.txt'
    if os.path.exists(summary_file):
        print("📊 关键结果摘要:")
        try:
            with open(summary_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 查找关键结果行
                for line in lines:
                    if any(keyword in line for keyword in ['准确率', '压缩比', '剪枝比例']):
                        print(f"   {line.strip()}")
        except Exception as e:
            print(f"   读取摘要文件失败: {e}")
    
    print("\n📁 所有结果已保存到 /download/ 目录中")
    print("   - 模型文件: /download/model/")
    print("   - 日志和摘要: /download/other/")
    print("=" * 70)

# def main():
def main(argv=None):
    parser = argparse.ArgumentParser(description='BERT剪枝运行脚本 - Modal平台版')
    
    # 运行模式选择
    parser.add_argument('--quick_test', action='store_true', 
                       help='快速测试模式（1000样本，1轮训练）')
    parser.add_argument('--std_pruning', action='store_true',
                       help='标准差剪枝模式')
    parser.add_argument('--percentile_pruning', action='store_true',
                       help='百分位数剪枝模式')
    parser.add_argument('--custom', action='store_true',
                       help='自定义配置模式')
    
    # 参数配置
    parser.add_argument('--sensitivity', type=float, default=0.25,
                       help='标准差剪枝敏感度 (默认: 0.25)')
    parser.add_argument('--percentile', type=float, default=10.0,
                       help='百分位数剪枝百分位数 (默认: 10.0)')
    parser.add_argument('--epochs', type=int, default=3,
                       help='微调轮数 (默认: 3)')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='批次大小 (默认: 16)')
    parser.add_argument('--learning_rate', type=float, default=2e-5,
                       help='学习率 (默认: 2e-5)')
    parser.add_argument('--max_samples', type=int, default=None,
                       help='最大样本数 (默认: None)')
    
    args = parser.parse_args(argv)
    
    # 打印启动横幅
    print_banner()
    script_dir = os.path.dirname(os.path.abspath(__file__))
    base_cmd = [sys.executable, os.path.join(script_dir, 'bert_pruning.py')]
    
    # 根据模式选择配置
    if args.quick_test:
        print_mode_info(
            "快速测试模式", 
            "使用1000个样本进行快速验证，适合测试和调试", 
            "5-10分钟"
        )
        cmd = base_cmd + [
            '--max_samples', '1000',
            '--epochs', '1',
            '--batch_size', '8',
            '--prune_method', 'std',
            '--sensitivity', '0.3'
        ]
    
    elif args.std_pruning:
        print_mode_info(
            "标准差剪枝模式", 
            "使用标准差方法进行剪枝，平衡压缩率和准确率", 
            "30-60分钟"
        )
        cmd = base_cmd + [
            '--prune_method', 'std',
            '--sensitivity', str(args.sensitivity),
            '--epochs', str(args.epochs),
            '--batch_size', str(args.batch_size),
            '--learning_rate', str(args.learning_rate)
        ]
        if args.max_samples:
            cmd.extend(['--max_samples', str(args.max_samples)])
        
        # 为了避免数据加载问题，先使用较小的batch size进行测试
        if args.batch_size > 8:
            print(f"⚠️  将batch_size从{args.batch_size}减小到8以避免内存和数据加载问题")
            cmd = [arg if arg != str(args.batch_size) else '8' for arg in cmd]
    
    elif args.percentile_pruning:
        print_mode_info(
            "百分位数剪枝模式", 
            "使用百分位数方法进行剪枝，精确控制剪枝比例", 
            "30-60分钟"
        )
        cmd = base_cmd + [
            '--prune_method', 'percentile',
            '--percentile', str(args.percentile),
            '--epochs', str(args.epochs),
            '--batch_size', str(args.batch_size),
            '--learning_rate', str(args.learning_rate)
        ]
        if args.max_samples:
            cmd.extend(['--max_samples', str(args.max_samples)])
    
    elif args.custom:
        print_mode_info(
            "自定义配置模式", 
            "使用用户指定的自定义参数配置", 
            "根据配置而定"
        )
        cmd = base_cmd + [
            '--prune_method', 'std',
            '--sensitivity', str(args.sensitivity),
            '--epochs', str(args.epochs),
            '--batch_size', str(args.batch_size),
            '--learning_rate', str(args.learning_rate)
        ]
        if args.max_samples:
            cmd.extend(['--max_samples', str(args.max_samples)])
    
    else:
        print("❌ 请选择一个运行模式:")
        print("   --quick_test        快速测试")
        print("   --std_pruning       标准差剪枝")
        print("   --percentile_pruning 百分位数剪枝")
        print("   --custom            自定义配置")
        print("\n使用 --help 查看完整帮助")
        return False
    
    # 显示最终命令
    print(f"📋 最终执行的命令参数:")
    for i, arg in enumerate(cmd[2:], 1):  # 跳过python和脚本名
        if i % 2 == 1:
            print(f"   {arg}", end="")
        else:
            print(f" {arg}")
    print("\n")
    
    # 执行命令
    success = run_command(cmd)
    
    # 检查保存的文件
    check_saved_files()
    
    # 打印总结
    if success:
        print_summary()
    else:
        print("\n💥 任务执行失败！请检查上面的错误信息")
        print("常见问题:")
        print("  - 检查网络连接（下载模型和数据集）")
        print("  - 检查GPU/CPU资源是否充足")
        print("  - 检查依赖包是否正确安装")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)