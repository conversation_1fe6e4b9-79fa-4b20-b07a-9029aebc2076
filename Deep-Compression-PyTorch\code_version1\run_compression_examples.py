#!/usr/bin/env python3
"""
BERT模型压缩示例脚本
演示如何使用灵活的压缩管道进行不同的压缩实验
"""

import subprocess
import sys
import os

def run_example(cmd, description):
    """运行示例命令"""
    print(f"\n🔥 {description}")
    print("="*60)
    print(f"命令: {' '.join(cmd)}")
    print("="*60)
    
    # 询问用户是否执行
    choice = input("是否执行此示例? (y/n): ").strip().lower()
    if choice in ['y', 'yes']:
        try:
            subprocess.run(cmd, check=True)
            print(f"✅ {description} 完成!")
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} 失败: {e}")
            return False
        except KeyboardInterrupt:
            print(f"⚠️ {description} 被中断")
            return False
    else:
        print(f"⏭️ 跳过 {description}")
    
    return True

def main():
    print("🚀 BERT模型Deep Compression压缩示例")
    print("="*60)
    print("本脚本展示各种压缩方案的使用方法")
    print("每个示例都会询问是否执行")
    print("="*60)
    
    script_path = os.path.join(os.path.dirname(__file__), "run_full_compression.py")
    
    examples = [
        # 1. 完整压缩流程示例
        {
            "cmd": [
                sys.executable, script_path,
                "--model_name", "demo_full_compression",
                "--steps", "all",
                "--max_samples", "1000",  # 快速演示
                "--epochs", "1"
            ],
            "description": "完整压缩流程演示 (剪枝+量化+哈夫曼编码)"
        },
        
        # 2. 仅剪枝示例
        {
            "cmd": [
                sys.executable, script_path,
                "--model_name", "demo_pruning_only",
                "--steps", "pruning",
                "--prune_method", "std",
                "--sensitivity", "0.25",
                "--max_samples", "1000",
                "--epochs", "1"
            ],
            "description": "仅权重剪枝演示 (标准差方法)"
        },
        
        # 3. 仅量化示例
        {
            "cmd": [
                sys.executable, script_path,
                "--model_name", "demo_quantization_only",
                "--steps", "quantization", 
                "--num_bits", "8"
            ],
            "description": "仅权重量化演示 (8位量化)"
        },
        
        # 4. 仅哈夫曼编码示例
        {
            "cmd": [
                sys.executable, script_path,
                "--model_name", "demo_huffman_only",
                "--steps", "huffman"
            ],
            "description": "仅哈夫曼编码演示 (需要先有8位量化模型)"
        },
        
        # 5. 剪枝+量化示例
        {
            "cmd": [
                sys.executable, script_path,
                "--model_name", "demo_prune_quant",
                "--steps", "pruning+quantization",
                "--prune_method", "percentile",
                "--percentile", "10.0",
                "--num_bits", "6",
                "--max_samples", "1000",
                "--epochs", "1"
            ],
            "description": "剪枝+量化组合演示 (百分位数剪枝 + 6位量化)"
        },
        
        # 6. 量化+哈夫曼编码示例
        {
            "cmd": [
                sys.executable, script_path,
                "--model_name", "demo_quant_huffman",
                "--steps", "quantization+huffman",
                "--num_bits", "4"
            ],
            "description": "量化+哈夫曼编码组合演示 (4位量化)"
        },
        
        # 7. 高压缩比示例
        {
            "cmd": [
                sys.executable, script_path,
                "--model_name", "demo_high_compression",
                "--steps", "all",
                "--prune_method", "std",
                "--sensitivity", "0.4",  # 更激进的剪枝
                "--num_bits", "4",       # 更低的位宽
                "--max_samples", "2000",
                "--epochs", "2"
            ],
            "description": "高压缩比实验 (激进剪枝 + 4位量化 + 哈夫曼编码)"
        }
    ]
    
    # 显示所有示例
    print("\n📋 可用示例:")
    for i, example in enumerate(examples, 1):
        print(f"   {i}. {example['description']}")
    
    print("\n" + "="*60)
    
    # 执行示例
    for i, example in enumerate(examples, 1):
        print(f"\n📍 示例 {i}/{len(examples)}")
        success = run_example(example["cmd"], example["description"])
        
        if not success:
            break_choice = input("\n是否继续下一个示例? (y/n): ").strip().lower()
            if break_choice not in ['y', 'yes']:
                break
    
    print(f"\n🎉 示例演示结束!")
    print(f"\n📁 查看生成的文件:")
    print(f"   📂 模型文件: /download/model/")
    print(f"   📂 日志文件: /download/other/")
    
    print(f"\n💡 提示:")
    print(f"   - 每个示例使用不同的model_name避免文件冲突")
    print(f"   - 演示使用较少的样本数和轮数以节省时间")
    print(f"   - 实际使用时可以调整参数获得更好效果")
    print(f"   - 详细用法请参考 FLEXIBLE_COMPRESSION_GUIDE.md")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 演示被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 演示过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)