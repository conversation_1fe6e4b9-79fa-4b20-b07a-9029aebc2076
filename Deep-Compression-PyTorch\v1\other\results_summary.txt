BERT模型剪枝结果摘要
==================================================

实验配置:
- 剪枝方法: std
- 敏感度: 0.25
- 微调轮数: 3
- 学习率: 2e-05
- 批次大小: 8
- 最大样本数: 全部

准确率结果:
- 原始模型准确率: 0.9514
- 剪枝后准确率: 0.9517
- 微调后准确率: 0.9437
- 准确率下降: 0.0078 (0.82%)

压缩统计:
- 总参数数: 85,527,552
- 剪枝参数数: 17,628,152
- 剪枝比例: 20.61%
- 压缩比: 1.26x

保存的模型文件:
- 原始模型: /download/model/original_model.pt
- 剪枝后模型: /download/model/pruned_model.pt
- 最终模型: /download/model/final_model.pt

层级剪枝详情（前20层）:
层名称                                      剪枝比例       剪枝参数         总参数         
--------------------------------------------------------------------------------
bert.pooler.dense                        28.69     % 169,237      589,824     
bert.encoder.layer.2.attention.self.key  21.53     % 127,000      589,824     
bert.encoder.layer.4.output.dense        21.50     % 507,355      2,359,296   
bert.encoder.layer.0.attention.output.dense 21.45     % 126,515      589,824     
bert.encoder.layer.2.attention.self.query 21.44     % 126,480      589,824     
bert.encoder.layer.5.output.dense        21.44     % 505,741      2,359,296   
bert.encoder.layer.1.attention.output.dense 21.39     % 126,134      589,824     
bert.encoder.layer.6.output.dense        21.29     % 502,270      2,359,296   
bert.encoder.layer.3.output.dense        21.25     % 501,417      2,359,296   
bert.encoder.layer.2.attention.output.dense 21.22     % 125,171      589,824     
bert.encoder.layer.10.attention.self.value 21.12     % 124,554      589,824     
bert.encoder.layer.0.attention.self.key  21.10     % 124,444      589,824     
bert.encoder.layer.5.attention.output.dense 21.06     % 124,227      589,824     
bert.encoder.layer.3.attention.output.dense 21.05     % 124,168      589,824     
bert.encoder.layer.6.attention.output.dense 21.05     % 124,150      589,824     
bert.encoder.layer.2.output.dense        21.04     % 496,334      2,359,296   
bert.encoder.layer.4.attention.output.dense 21.02     % 123,974      589,824     
bert.encoder.layer.1.output.dense        20.96     % 494,427      2,359,296   
bert.encoder.layer.7.output.dense        20.92     % 493,577      2,359,296   
bert.encoder.layer.5.attention.self.value 20.90     % 123,256      589,824     
