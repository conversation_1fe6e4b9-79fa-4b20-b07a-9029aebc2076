# BERT剪枝项目 - 数据加载错误修复

## 🐛 问题描述

在运行标准剪枝命令时遇到以下错误：

```
RuntimeError: stack expects each tensor to be equal size, but got [200] at entry 0 and [217] at entry 8
```

**错误原因**: 数据加载器中的张量长度不一致，导致batch collation失败。

## 🔧 解决方案

### 问题根源

原始代码在tokenization阶段使用了 `padding=True`，但这只是在单个batch的tokenization时进行padding。当DataLoader尝试将多个样本组合成batch时，不同长度的序列无法正确堆叠。

### 修复方法

1. **移除tokenization阶段的padding** - 在`tokenize_function`中设置`padding=False`
2. **实现自定义collate函数** - 在DataLoader中使用自定义的collate_fn处理变长序列
3. **动态padding** - 在每个batch中动态padding到该batch的最大长度

### 核心代码修改

#### 修改前

```python
def tokenize_function(examples):
    return tokenizer(examples["text"], truncation=True, padding=True, max_length=512)

train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
```

#### 修改后

```python
def tokenize_function(examples):
    # 不在这里padding，而是在collate_fn中动态padding
    return tokenizer(examples["text"], truncation=True, max_length=512)

def collate_fn(batch):
    """自定义的collate函数，处理变长序列"""
    input_ids = [item['input_ids'] for item in batch]
    attention_masks = [item['attention_mask'] for item in batch]
    labels = [item['label'] for item in batch]
    
    # Padding到批次中的最大长度
    max_len = max(len(ids) for ids in input_ids)
    
    padded_input_ids = []
    padded_attention_masks = []
    
    for ids, mask in zip(input_ids, attention_masks):
        pad_len = max_len - len(ids)
        
        # Padding input_ids（使用tokenizer的pad_token_id）
        padded_ids = torch.cat([ids, torch.full((pad_len,), tokenizer.pad_token_id, dtype=ids.dtype)])
        padded_input_ids.append(padded_ids)
        
        # Padding attention_mask（使用0）
        padded_mask = torch.cat([mask, torch.zeros(pad_len, dtype=mask.dtype)])
        padded_attention_masks.append(padded_mask)
    
    return {
        'input_ids': torch.stack(padded_input_ids),
        'attention_mask': torch.stack(padded_attention_masks),
        'label': torch.stack(labels)
    }

train_loader = torch.utils.data.DataLoader(
    train_dataset, 
    batch_size=batch_size, 
    shuffle=True,
    collate_fn=collate_fn
)
```

## 🔍 修复验证

### 添加的测试功能

1. **数据加载器测试** - 新增`test_data_loader_fix()`函数
2. **批次形状验证** - 检查每个batch的张量形状是否一致
3. **错误处理增强** - 更详细的错误信息和调试信息

### 运行测试

```bash
python test_bert_pruning.py
```

### 验证修复后的主程序

```bash
# 快速测试
python run_bert_pruning_modal.py --quick_test

# 标准剪枝（修复后）
python run_bert_pruning_modal.py --std_pruning --sensitivity 0.25
```

## ✅ 修复效果

### 修复前

- ❌ 运行时张量长度不匹配错误
- ❌ 无法完成数据评估阶段
- ❌ 程序在13%处崩溃

### 修复后

- ✅ 正确处理变长序列
- ✅ 动态padding确保batch一致性
- ✅ 完整的训练和评估流程
- ✅ 所有保存文件正确生成

## 🚀 附加改进

### 1. 增强的错误处理

- 数据加载器创建时的异常捕获
- 详细的调试信息输出
- 批次形状验证

### 2. 优化的运行脚本

- 自动调整batch_size避免内存问题
- 更好的错误提示和解决建议
- 实时进度反馈

### 3. 完善的测试套件

- 专门的数据加载器测试
- 多个批次的验证
- 完整的回归测试

## 📊 性能影响

- **内存使用**: 动态padding相比固定padding更高效
- **训练速度**: 基本无影响，可能略有提升
- **准确性**: 无影响，数据处理逻辑保持一致

## 🔮 后续优化建议

1. **进一步优化**: 可以考虑使用Hugging Face的`DataCollatorWithPadding`
2. **缓存机制**: 对tokenized数据进行缓存以提升重复运行速度
3. **混合精度**: 添加FP16支持以节省内存和提升速度

---

**修复状态**: ✅ 已完成并测试
**影响范围**: 数据加载和批处理逻辑
**兼容性**: 完全向后兼容，不影响其他功能
