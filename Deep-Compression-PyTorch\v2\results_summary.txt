BERT模型剪枝结果摘要
==================================================

实验配置:
- 剪枝方法: std
- 敏感度: 1.5
- 微调轮数: 3
- 学习率: 2e-05
- 批次大小: 8
- 最大样本数: 全部

准确率结果:
- 原始模型准确率: 0.9514
- 剪枝后准确率: 0.2821
- 微调后准确率: 0.9296
- 准确率下降: 0.0218 (2.30%)

压缩统计:
- 总参数数: 85,527,552
- 剪枝参数数: 74,588,423
- 剪枝比例: 87.21%
- 压缩比: 7.82x

保存的模型文件:
- 原始模型: /download/model/original_model.pt
- 剪枝后模型: /download/model/pruned_model.pt
- 最终模型: /download/model/final_model.pt

层级剪枝详情（前20层）:
层名称                                      剪枝比例       剪枝参数         总参数         
--------------------------------------------------------------------------------
bert.pooler.dense                        90.75     % 535,271      589,824     
bert.encoder.layer.0.attention.output.dense 88.10     % 519,625      589,824     
bert.encoder.layer.2.attention.self.query 88.10     % 519,624      589,824     
bert.encoder.layer.1.attention.output.dense 88.09     % 519,564      589,824     
bert.encoder.layer.2.attention.self.key  88.09     % 519,555      589,824     
bert.encoder.layer.3.output.dense        87.90     % 2,073,879    2,359,296   
bert.encoder.layer.4.output.dense        87.88     % 2,073,358    2,359,296   
bert.encoder.layer.5.output.dense        87.83     % 2,072,145    2,359,296   
bert.encoder.layer.1.output.dense        87.76     % 2,070,445    2,359,296   
bert.encoder.layer.9.output.dense        87.74     % 2,070,066    2,359,296   
bert.encoder.layer.6.output.dense        87.62     % 2,067,227    2,359,296   
bert.encoder.layer.0.attention.self.key  87.62     % 516,792      589,824     
bert.encoder.layer.2.output.dense        87.59     % 2,066,498    2,359,296   
bert.encoder.layer.0.output.dense        87.53     % 2,065,187    2,359,296   
bert.encoder.layer.2.attention.output.dense 87.49     % 516,057      589,824     
bert.encoder.layer.3.attention.output.dense 87.41     % 515,588      589,824     
bert.encoder.layer.7.output.dense        87.40     % 2,062,008    2,359,296   
bert.encoder.layer.8.output.dense        87.35     % 2,060,799    2,359,296   
bert.encoder.layer.10.output.dense       87.33     % 2,060,309    2,359,296   
bert.encoder.layer.0.attention.self.query 87.30     % 514,916      589,824     
