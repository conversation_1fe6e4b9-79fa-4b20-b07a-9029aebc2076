# BERT模型剪枝项目

基于Deep Compression论文的剪枝算法，专门针对BERT模型（textattack/bert-base-uncased-ag-news）进行结构化剪枝。

## 📋 功能特性

- ✅ **两种剪枝方法**: 百分位数剪枝和标准差剪枝
- ✅ **智能掩码管理**: 自动为所有线性层创建和管理剪枝掩码
- ✅ **完整的训练流程**: 原始评估 → 剪枝 → 微调 → 最终评估
- ✅ **详细统计信息**: 层级和全局的剪枝统计
- ✅ **模型保存**: 自动保存原始、剪枝后和最终模型
- ✅ **灵活配置**: 支持多种参数配置和快速测试模式

## 🔧 环境要求

### Python版本
- Python 3.7+

### 依赖安装
```bash
pip install -r requirements_bert.txt
```

或者手动安装：
```bash
pip install torch>=1.8.0 transformers>=4.20.0 datasets>=2.0.0 numpy>=1.21.0 scikit-learn>=1.0.0 tqdm>=4.64.0 accelerate>=0.20.0
```

## 🚀 快速开始

### Modal平台版本（推荐）

#### 1. 快速测试（推荐初次使用）
```bash
python run_bert_pruning_modal.py --quick_test
```
这将使用1000个样本进行快速测试，约5-10分钟完成。

#### 2. 标准差剪枝（推荐）
```bash
python run_bert_pruning_modal.py --std_pruning --sensitivity 0.25
```

#### 3. 百分位数剪枝
```bash
python run_bert_pruning_modal.py --percentile_pruning --percentile 10.0
```

#### 4. 自定义配置
```bash
python run_bert_pruning_modal.py --custom --sensitivity 0.3 --epochs 5
```

### 标准版本（本地运行）

#### 1. 快速测试
```bash
python run_bert_pruning.py --quick_test
```

#### 2. 标准差剪枝
```bash
python run_bert_pruning.py --std_pruning --sensitivity 0.25
```

#### 3. 百分位数剪枝
```bash
python run_bert_pruning.py --percentile_pruning --percentile 10.0
```

## 📚 详细使用方法

### 直接调用主脚本
```bash
python bert_pruning.py [参数]
```

### 重要参数说明

| 参数 | 说明 | 默认值 | 推荐值 |
|------|------|--------|--------|
| `--prune_method` | 剪枝方法: `std` 或 `percentile` | `std` | `std` |
| `--sensitivity` | 标准差剪枝敏感度 | 0.25 | 0.2-0.3 |
| `--percentile` | 百分位数剪枝百分位数 | 5.0 | 5.0-15.0 |
| `--epochs` | 微调训练轮数 | 3 | 3-5 |
| `--batch_size` | 批次大小 | 16 | 8-32 |
| `--learning_rate` | 学习率 | 2e-5 | 1e-5 - 5e-5 |
| `--max_samples` | 最大样本数（测试用） | None | 1000-5000 |

### 示例命令

#### 1. 轻量级剪枝（保持高准确率）
```bash
python bert_pruning.py --sensitivity 0.2 --epochs 5 --learning_rate 1e-5
```

#### 2. 激进剪枝（更高压缩比）
```bash
python bert_pruning.py --sensitivity 0.4 --epochs 3 --learning_rate 3e-5
```

#### 3. 百分位数剪枝测试
```bash
python bert_pruning.py --prune_method percentile --percentile 15.0 --max_samples 2000
```

## 📊 输出结果解读

### 运行过程
1. **模型加载**: 加载预训练的BERT模型
2. **原始评估**: 评估未剪枝模型的性能
3. **执行剪枝**: 根据选择的方法进行权重剪枝
4. **剪枝评估**: 评估剪枝后模型的性能下降
5. **微调训练**: 对剪枝后的模型进行微调
6. **最终评估**: 评估微调后的最终性能

### 关键指标
- **压缩比**: 模型大小的压缩倍数（如2.5x表示模型大小减小到原来的1/2.5）
- **剪枝比例**: 被剪枝的参数百分比
- **准确率下降**: 相对于原始模型的准确率下降幅度

### 典型结果示例
```
=== 最终结果总结 ===
原始模型准确率: 0.9342
剪枝后准确率: 0.8956
微调后准确率: 0.9298
准确率下降: 0.0044 (0.47%)
模型压缩比: 2.85x
剪枝比例: 64.91%
```

## 📁 输出文件结构

### Modal平台版本
```
/download/
├── model/
│   ├── original_model.pt      # 原始模型权重
│   ├── pruned_model.pt        # 剪枝后模型权重
│   └── final_model.pt         # 微调后最终模型权重
└── other/
    ├── bert_pruning.log       # 详细运行日志
    └── results_summary.txt    # 结果摘要报告
```

### 标准版本（本地）
```
bert_saves/
├── original_model.pt      # 原始模型权重
├── pruned_model.pt        # 剪枝后模型权重
└── final_model.pt         # 微调后最终模型权重
```

## 🔍 核心算法说明

### 1. 剪枝策略

#### 标准差剪枝（推荐）
- **原理**: 对每一层计算权重的标准差，阈值 = std × sensitivity
- **优点**: 能够自适应每层的权重分布特性
- **适用**: 大多数情况下效果最好

#### 百分位数剪枝
- **原理**: 全局计算所有权重的百分位数作为阈值
- **优点**: 能保证精确的剪枝比例
- **适用**: 需要严格控制剪枝比例时

### 2. 掩码机制
- 为每个线性层创建二进制掩码
- 前向传播时自动应用掩码
- 反向传播时确保剪枝位置梯度为0

### 3. 微调策略
- 使用AdamW优化器
- 较小的学习率（2e-5）
- 权重衰减正则化（0.01）

## ⚡ 性能优化建议

### 1. 硬件要求
- **推荐**: GPU (8GB+ 显存)
- **最低**: CPU (运行较慢)

### 2. 内存优化
- 使用`--max_samples`限制数据集大小进行测试
- 减小`--batch_size`如果遇到内存不足

### 3. 时间估算
- **快速测试**: 5-10分钟 (1000样本, 1 epoch)
- **标准运行**: 30-60分钟 (全数据, 3 epochs)
- **完整训练**: 1-2小时 (全数据, 5 epochs)

## 🎯 调优建议

### 根据目标选择参数

#### 优先准确率（轻量剪枝）
```bash
python bert_pruning.py --sensitivity 0.15 --epochs 5 --learning_rate 1e-5
```

#### 平衡准确率和压缩（标准剪枝）
```bash
python bert_pruning.py --sensitivity 0.25 --epochs 3 --learning_rate 2e-5
```

#### 优先压缩比（激进剪枝）
```bash
python bert_pruning.py --sensitivity 0.4 --epochs 4 --learning_rate 3e-5
```

## 🐛 常见问题

### 1. 内存不足
**解决方案**: 
- 减小batch_size: `--batch_size 8`
- 使用CPU: `--no_cuda`
- 限制样本数: `--max_samples 1000`

### 2. CUDA不可用
**解决方案**: 添加`--no_cuda`参数强制使用CPU

### 3. 数据集下载失败
**解决方案**: 
- 检查网络连接
- 使用代理或VPN
- 手动下载数据集到~/.cache/huggingface/

### 4. 剪枝效果不理想
**解决方案**:
- 降低敏感度参数（如0.25→0.2）
- 增加微调轮数（如3→5）
- 尝试不同的剪枝方法

## 📈 扩展开发

### 1. 添加新的剪枝方法
继承`BERTPruningModule`类并实现新的剪枝函数：
```python
def prune_by_custom_method(self, **kwargs):
    # 实现自定义剪枝逻辑
    pass
```

### 2. 支持其他BERT模型
修改`--model_name`参数即可：
```bash
python bert_pruning.py --model_name bert-base-uncased
```

### 3. 添加更多统计信息
在`get_pruning_stats`方法中添加新的统计指标。

## 📄 参考文献

1. **Deep Compression**: Han, S., Mao, H., & Dally, W. J. (2015). Deep compression: Compressing deep neural networks with pruning, trained quantization and huffman coding.

2. **BERT**: Devlin, J., Chang, M. W., Lee, K., & Toutanova, K. (2018). Bert: Pre-training of deep bidirectional transformers for language understanding.

## 📞 技术支持

如果遇到问题，请检查：
1. Python和依赖包版本是否满足要求
2. 硬件资源是否充足
3. 网络连接是否正常

---

**Happy Pruning! 🌳✂️**