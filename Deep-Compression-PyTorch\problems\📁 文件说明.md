## 📁 文件说明

我创建了5个文件，但**你只需要关注2个主要文件**：

### 🎯 **主要文件（必须的）**

1. **`bert_pruning.py`** - 这是**核心文件**，包含所有剪枝功能
2. **`requirements_bert.txt`** - 依赖包列表

### 🛠️ **辅助文件（可选的）**

3. **`run_bert_pruning.py`** - 简化的运行脚本（让使用更简单）
4. **`test_bert_pruning.py`** - 测试脚本（验证功能是否正常）
5. **`example_usage.py`** - 使用示例（学习用）
6. **`BERT_PRUNING_README.md`** - 详细说明文档

## 🚀 **快速开始（3步搞定）**

### 第1步：安装依赖
```bash
pip install -r requirements_bert.txt
```

### 第2步：选择一种运行方式

#### 方式A：直接运行主文件（推荐新手）
```bash
# 快速测试（5分钟）
python bert_pruning.py --max_samples 1000 --epochs 1 --batch_size 8

# 标准运行（30分钟）
python bert_pruning.py --sensitivity 0.25 --epochs 3
```

#### 方式B：使用简化脚本（更简单）
```bash
# 快速测试
python run_bert_pruning.py --quick_test

# 标准剪枝
python run_bert_pruning.py --std_pruning
```

### 第3步：查看结果
运行完成后，会在 `bert_saves/` 目录中保存3个模型文件。

## 💡 **推荐使用方法**

**如果你是第一次使用**，建议：

1. **先快速测试**：
```bash
python run_bert_pruning.py --quick_test
```

2. **如果测试成功，再正式运行**：
```bash
python run_bert_pruning.py --std_pruning --sensitivity 0.25
```

## 🤔 **为什么创建这么多文件？**

- **`bert_pruning.py`** - 核心功能，必须有
- **`run_bert_pruning.py`** - 让你不用记复杂的参数
- **其他文件** - 测试和学习用，可以忽略

## ⚡ **最简单的运行方式**

如果你想最简单地运行，就这样：

```bash
# 1. 安装依赖
pip install torch transformers datasets numpy scikit-learn tqdm

# 2. 直接运行（一行命令）
python bert_pruning.py --max_samples 1000 --epochs 1 --prune_method std --sensitivity 0.3
```

这会用1000个样本快速测试剪枝效果，大概5-10分钟完成。

你想先试试这个最简单的方式吗？