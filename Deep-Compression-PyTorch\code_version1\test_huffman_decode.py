#!/usr/bin/env python3
"""
测试哈夫曼编码模型的解码和推理功能
验证Deep Compression管道的完整性
"""

import os
import sys
import torch
import json
from datetime import datetime
from transformers import AutoTokenizer

# 添加当前目录到路径以便导入bert_huffman_encode模块
sys.path.append(os.path.dirname(__file__))
from bert_huffman_encode import decode_huffman_encoded_model, evaluate_model, safe_torch_load

def find_latest_huffman_model():
    """查找最新的哈夫曼编码模型目录"""
    encoding_base_dir = "/download/model/huffman_encoded"
    
    if os.path.exists(encoding_base_dir):
        return encoding_base_dir
    
    # 如果没有直接的编码目录，查找tar.gz文件并解压
    import glob
    import tarfile
    
    archives = glob.glob("/download/model/bert_huffman_encoded_*.tar.gz")
    if not archives:
        return None
    
    # 使用最新的压缩包
    latest_archive = max(archives, key=os.path.getctime)
    print(f"🔄 发现压缩包: {latest_archive}")
    
    # 解压到临时目录
    extract_dir = "/download/model/temp_huffman_extract"
    os.makedirs(extract_dir, exist_ok=True)
    
    with tarfile.open(latest_archive, "r:gz") as tar:
        tar.extractall(extract_dir)
    
    # 查找解压后的编码目录
    extracted_dirs = [d for d in os.listdir(extract_dir) if d.startswith("bert_huffman_encoded_")]
    if extracted_dirs:
        return os.path.join(extract_dir, extracted_dirs[0])
    
    return None

def test_model_inference(model, tokenizer, device):
    """测试模型推理功能"""
    print("\n🧪 测试模型推理功能...")
    
    # 测试文本和标签
    test_cases = [
        ("The stock market soared to record highs today after positive earnings reports.", "Business"),
        ("Scientists discovered a new exoplanet using advanced telescope technology.", "Sci/Tech"),  
        ("The championship game will be played this Sunday at the stadium.", "Sports"),
        ("International leaders met to discuss climate change policies.", "World")
    ]
    
    class_names = ["World", "Sports", "Business", "Sci/Tech"]
    model.eval()
    
    print("📝 测试结果:")
    print("-" * 80)
    print(f"{'文本':<50} {'预测':<10} {'置信度':<8}")
    print("-" * 80)
    
    with torch.no_grad():
        for text, expected in test_cases:
            # 分词和编码
            inputs = tokenizer(
                text, 
                return_tensors="pt", 
                truncation=True, 
                padding=True, 
                max_length=512
            ).to(device)
            
            # 预测
            outputs = model(**inputs)
            predicted_idx = outputs.logits.argmax(dim=-1).item()
            confidence = torch.softmax(outputs.logits, dim=-1).max().item()
            predicted_class = class_names[predicted_idx]
            
            # 显示结果
            text_short = text[:47] + "..." if len(text) > 50 else text
            status = "✅" if predicted_class == expected else "❌"
            print(f"{text_short:<50} {predicted_class:<10} {confidence:<8.3f} {status}")
    
    print("-" * 80)

def compare_model_sizes():
    """比较各阶段模型大小"""
    print("\n📊 模型大小对比:")
    print("-" * 50)
    
    # 检查各阶段文件
    files = [
        ("原始模型", "/download/model/original_model.pt"),
        ("剪枝后模型", "/download/model/pruned_model.pt"),
        ("微调后模型", "/download/model/final_model.pt"),
        ("量化模型(FP32)", "/download/model/quantized_model.pt"),
        ("量化模型(8bit)", "/download/model/quantized_model_8bit.pt"),
    ]
    
    original_size = None
    
    for name, path in files:
        if os.path.exists(path):
            size_mb = os.path.getsize(path) / 1024 / 1024
            
            if name == "原始模型":
                original_size = size_mb
                print(f"   {name}: {size_mb:.2f} MB (基准)")
            else:
                if original_size:
                    compression = original_size / size_mb
                    reduction = (1 - size_mb / original_size) * 100
                    print(f"   {name}: {size_mb:.2f} MB ({compression:.2f}x, -{reduction:.1f}%)")
                else:
                    print(f"   {name}: {size_mb:.2f} MB")
        else:
            print(f"   {name}: 文件不存在")
    
    # 检查哈夫曼编码压缩包
    import glob
    archives = glob.glob("/download/model/bert_huffman_encoded_*.tar.gz")
    if archives:
        latest_archive = max(archives, key=os.path.getctime)
        archive_size = os.path.getsize(latest_archive) / 1024 / 1024
        
        if original_size:
            compression = original_size / archive_size
            reduction = (1 - archive_size / original_size) * 100
            print(f"   最终压缩包: {archive_size:.2f} MB ({compression:.2f}x, -{reduction:.1f}%)")
        else:
            print(f"   最终压缩包: {archive_size:.2f} MB")

def main():
    print("🔬 哈夫曼编码模型解码测试")
    print("="*50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 查找哈夫曼编码模型
    print("\n🔍 查找哈夫曼编码模型...")
    encoding_dir = find_latest_huffman_model()
    
    if not encoding_dir:
        print("❌ 未找到哈夫曼编码模型")
        print("💡 请确保已运行哈夫曼编码脚本")
        return False
    
    print(f"✅ 找到编码模型: {encoding_dir}")
    
    try:
        # 加载分词器
        print("\n🔄 加载分词器...")
        tokenizer = AutoTokenizer.from_pretrained("textattack/bert-base-uncased-ag-news")
        
        # 解码哈夫曼编码模型
        print("\n🔄 解码哈夫曼编码模型...")
        model = decode_huffman_encoded_model(encoding_dir, device)
        
        # 计算模型参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"📊 模型信息:")
        print(f"   总参数数: {total_params:,}")
        print(f"   可训练参数数: {trainable_params:,}")
        
        # 测试推理功能
        test_model_inference(model, tokenizer, device)
        
        # 性能评估
        print("\n📈 性能评估...")
        accuracy = evaluate_model(model, tokenizer, device, sample_size=500)
        print(f"✅ 模型准确率: {accuracy:.4f}")
        
        # 模型大小对比
        compare_model_sizes()
        
        print("\n🎉 哈夫曼编码模型测试完成!")
        print("✅ 解码成功，模型功能正常")
        print("💡 Deep Compression压缩管道验证通过")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    
    if not success:
        print("\n❌ 测试失败")
        sys.exit(1)
    else:
        print("\n" + "="*50)
        print("🏆 Deep Compression测试总结:")
        print("   ✅ 剪枝: 移除冗余权重连接")
        print("   ✅ 量化: 32位→8位权重压缩")
        print("   ✅ 哈夫曼: 熵编码进一步压缩")
        print("   ✅ 解码: 完整恢复模型功能")
        print("   ✅ 推理: 保持模型预测能力")
        print("="*50)